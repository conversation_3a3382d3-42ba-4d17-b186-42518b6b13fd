import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function POST(req: NextApiRequest, res: NextApiResponse) {
  const body = req.body;
  const url = `${BACKEND_URL}/${API_PATHS.CHAT_INVOKE}`;
  const token = await getAuthToken(req);

  try {
    const response = await axios.post(url, body, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    console.log("response invoke", response)
    res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      res.status(500).json({
        message: 'An unknown error occurred',
      });
    }
  }
}
