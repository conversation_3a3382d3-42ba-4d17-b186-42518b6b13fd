import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import { applyCorsHeaders } from '@/utils/cors';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

const BACKEND_URL = getEnvConfig().backendUrl;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'DELETE') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const token = await getAuthToken(req);
    const { chatId, fileName } = req.query;

    if (!chatId) {
      return res.status(400).json({ message: 'Chat ID is required' });
    }

    if (!fileName) {
      return res.status(400).json({ message: 'File name is required' });
    }

    // Send delete request to backend
    const response = await axios.delete(
      `${BACKEND_URL}/${API_PATHS.CHAT_FILE_DELETE}`,
      {
        headers: {
          Authorization: token,
          'Content-Type': 'application/json',
        },
        params: {
          chat_id: chatId,
          file_name: fileName,
        },
      }
    );

    return res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      return res.status(500).json({
        message: 'An unknown error occurred',
      });
    }
  }
}
