import { getEnvConfig } from '@/env-config.zod';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import { applyCorsHeaders } from '@/utils/cors';
import axios from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';

const BACKEND_URL = getEnvConfig().backendUrl;

export const config = {
  api: {
    bodyParser: false, // Disable body parser to handle FormData
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  applyCorsHeaders(res, req.headers.origin || '');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const token = await getAuthToken(req);
    
    // Parse FormData
    const form = formidable();
    const [fields, files] = await form.parse(req);
    
    const data = fields.data?.[0];
    const file = files.file?.[0];
    
    if (!file) {
      return res.status(400).json({ message: 'No file provided' });
    }

    if (!data) {
      return res.status(400).json({ message: 'No data provided' });
    }

    // Create form data for the backend
    const FormData = require('form-data');
    const formData = new FormData();

    // Add the data field exactly as received
    formData.append('data', data);

    // Add the file with proper metadata
    formData.append('file', file.filepath, {
      filename: file.originalFilename || 'uploaded-file',
      contentType: file.mimetype || 'application/octet-stream',
    });

    // Send to backend with exact format
    const response = await axios.post(
      `${BACKEND_URL}/${API_PATHS.CHAT_FILE_UPLOAD}`,
      formData,
      {
        headers: {
          Authorization: token,
          ...formData.getHeaders(),
        },
      }
    );

    return res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status || 500).json({
        message: error.message,
        error: error.response?.data,
      });
    } else {
      return res.status(500).json({
        message: 'An unknown error occurred',
      });
    }
  }
}