import Layout, { LayoutType } from '@/components/layout';
import LoadingSpinner from '@/components/loading-spinner';
import { getEnvConfig } from '@/env-config.zod';
import { getFileNameCSV, getCsvDataForQATests } from '@/modules/i2r/utils/csv-builder';
import Markdown from '@/modules/platform/components/markdown';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import RegenerationModal from '@/modules/platform/components/regeneration-modal';
import { R2Q_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { Job } from '@/modules/platform/interfaces/job';
import { Modules, SubModules } from '@/modules/platform/interfaces/modules';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import { areJobsComplete, hasJobFailed } from '@/modules/platform/utils/job-status';
import OptionsMenu from '@/modules/r2q/components/options-menu';
import QATestsEdit from '@/modules/r2q/components/qa-tests-edit';
import { QA_TESTS_CSV_LABELS } from '@/modules/r2q/constants/qa-tests';
import { R2QPollingProvider, useR2QPollingContext } from '@/modules/r2q/contexts/polling.context';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { getMessages } from '@/utils/i18n';
import { getPlatformConfigs } from '@/utils/server/platform/config/api';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { Accordion, AccordionItem, Divider } from '@heroui/react';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { GetServerSidePropsContext } from 'next';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { CSVLink } from 'react-csv';
import {
  QATestsErrorTypes,
  QATestsErrorValues,
  ExtendedTicketData,
} from '@/modules/r2q/interfaces';
import { useFetchChat } from '@/modules/platform/utils/hooks/fetch-chat';
import UserMessage from '@/components/user-message';
import Checkbox from '@/components/checkbox';
import log from '@/utils/logger';
import { Version } from '@/modules/r2q/components/version';
import axiosInstance from '@/utils/axios';
import { ChatId } from '@/components/chat_id/chat_id';
import { multiplyRefetchInterval } from '@/utils/multiply-refetch-interval';
import { formatTextWithLineBreaks } from '@/utils/markdown-formatter';

const QATestsPageWrapper = () => {
  return (
    <R2QPollingProvider>
      <QATestsPage />
    </R2QPollingProvider>
  );
};

const QATestsPage = () => {
  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.R2Q,
  );

  const router = useRouter();
  const pathname = router.pathname;
  const onCreateNewRequest = () => router.push(`/${Modules.R2Q.toLocaleLowerCase()}`);

  const { 'chat-id': chatId } = router.query;

  const tabItems = useRequestHistoryTabItems(
    activeRequests,
    archivedRequests,
    refetchRequestHistory,
    Modules.R2Q,
    pathname,
    onCreateNewRequest,
  );

  const commonConstants = useTranslations('Common');
  const r2qConstants = useTranslations('R2Q');

  const {
    pollingEnabled,
    setPollingEnabled,
    pollingAfterPublish,
    setPollingAfterPublish,
    setRegeneratingTicketIds,
    regeneratingTicketIds,
    publishingTicketIds,
    setPublishingTicketIds,
    fetchAfterEdit,
    setFetchAfterEdit,
    pollingAfterRegeneration,
    setPollingAfterRegeneration,
  } = useR2QPollingContext();

  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editTest, setEditTest] = useState<ExtendedTicketData | null>(null);
  const [fetchTicketsEnabled, setFetchTicketsEnabled] = useState<boolean>(false);
  const [fetchAfterPublish, setFetchAfterPublish] = useState<boolean>(false);
  const [allQATests, setAllQATests] = useState<ExtendedTicketData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [regenerationId, setRegenerationId] = useState<string>('');
  const [error, setError] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = React.useState<string>(r2qConstants('errorMsg'));
  const [loadingTicketIds, setLoadingTicketIds] = useState<string[]>([]);
  const [previousTicketIds, setPreviousTicketIds] = useState<string[]>([]);
  const { data: chatMessage, isLoading, isError } = useFetchChat(chatId as string);

  const qaTestsByStoryId = allQATests.reduce(
    (testsGroupedByStoryId, test) => {
      const { story_id } = test;

      if (!testsGroupedByStoryId[story_id]) {
        testsGroupedByStoryId[story_id] = [];
      }

      testsGroupedByStoryId[story_id].push(test);
      return testsGroupedByStoryId;
    },
    {} as Record<string, ExtendedTicketData[]>,
  );

  const fetchJobs = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/jobs', { params: { chatId } });
      if (response.data.length === 0) {
        setError(true);
        setPollingEnabled(false);
        setFetchTicketsEnabled(false);
        return [];
      }
      const regenerationJobs = response.data.filter(
        (job: Job) => job.sub_type === SubModules.QA_TESTS && job.regeneration_job,
      );
      if (pollingAfterRegeneration && regenerationJobs.length > 0) {
        setPreviousTicketIds(regeneratingTicketIds[chatId as string] || []);
        if (areJobsComplete(regenerationJobs, SubModules.QA_TESTS)) {
          setPollingAfterRegeneration(false);
          await fetchRegeneratedTickets();
        }
        return [];
      }
      if (
        response.data.length > 0 &&
        areJobsComplete(response.data, SubModules.QA_TESTS) &&
        hasJobFailed(response.data[response.data.length - 1])
      ) {
        setError(true);
        if (
          response.data[0]?.metadata?.failure_type === QATestsErrorTypes.BAD_REQUEST &&
          response.data[0]?.metadata?.value === QATestsErrorValues.NO_QA_READY_STORIES
        ) {
          setErrorMessage(r2qConstants('noTicketsFoundErrorMsg'));
        }
        setPollingEnabled(false);
        setPollingAfterPublish(false);
        setFetchTicketsEnabled(false);
        return [];
      }

      if (areJobsComplete(regenerationJobs, SubModules.QA_TESTS)) {
        setRegeneratingTicketIds((prevState) => ({
          ...prevState,
          [chatId as string]: [],
        }));
      }

      if (
        areJobsComplete(response.data, SubModules.PMP) &&
        areJobsComplete(response.data, SubModules.QA_TESTS) &&
        areJobsComplete(response.data, SubModules.PUBLISH_TO_ZEPHYR)
      ) {
        setPollingAfterPublish(false);
        setPublishingTicketIds([]);
        setPollingEnabled(false);
        setFetchTicketsEnabled(true);
      }

      if (
        response.data.length > 0 &&
        response.data[0].sub_type === SubModules.PMP &&
        areJobsComplete(response.data, SubModules.PMP)
      ) {
        setPollingAfterPublish(false);
        setFetchAfterPublish(true);
      }

      return response.data;
    } catch (error) {
      setLoading(false);
      setPollingEnabled(false);
      setFetchTicketsEnabled(false);
      setError(true);
      log.warn('Error in fetching jobs:', error);
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchJobs', chatId],
    queryFn: () => fetchJobs(),
    enabled: !!chatId && (pollingEnabled || pollingAfterPublish || pollingAfterRegeneration),
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const handleEdit = async () => {
    try {
      setFetchAfterEdit(false);
      const response = await axiosInstance.get('/api/platform/ticket-version', {
        params: {
          versionId: editTest?.version,
          originalTicketId: editTest?.original_ticket_id,
        },
      });

      const updatedTest = response.data;
      updatedTest.isSelected = editTest?.isSelected;
      setAllQATests((prev) => {
        const updatedTests = prev.map((currentTest) => {
          if (currentTest.original_ticket_id === editTest?.original_ticket_id) {
            return updatedTest;
          }
          return currentTest;
        });
        return updatedTests;
      });
      return response.data;
    } catch (error) {
      log.warn('Error in fetching jobs:', error);
      return null;
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['handleEdit', chatId],
    queryFn: () => handleEdit(),
    enabled: !!chatId && fetchAfterEdit,
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  const fetchTickets = async () => {
    try {
      const response = await axiosInstance.get('/api/platform/tickets', { params: { chatId } });
      setFetchTicketsEnabled(false);
      setFetchAfterPublish(false);

      if (response.data.length === 0) {
        setPollingEnabled(true);
        return [];
      }

      const allTests = response.data.filter(
        (ticket: ExtendedTicketData) => ticket.type === SubModules.QA_TESTS,
      );

      const mappedQATests = (allTests as ExtendedTicketData[]).map((test) => {
        const testExists = qaTestsByStoryId[test.story_id]?.some((t) => t.id === test.id);
        return {
          ...test,
          isSelected: testExists
            ? qaTestsByStoryId[test.story_id].find((t) => t.id === test.id)?.isSelected
            : true,
        };
      });

      setAllQATests(mappedQATests);
      setLoading(false);
      setRegenerationId('');
      return response.data;
    } catch (err) {
      log.warn('Error Occurred while fetching tickets', err);
      setLoading(false);
      setError(true);
      return [];
    }
  };

  useQuery<Job[], Error>({
    queryKey: ['fetchTickets', chatId],
    queryFn: () => fetchTickets(),
    enabled: !!chatId && (fetchTicketsEnabled || fetchAfterPublish),
    refetchInterval: multiplyRefetchInterval,
  } as UseQueryOptions<Job[], Error>);

  useEffect(() => {
    setPollingAfterPublish(true);
    setLoading(true);
    setPollingEnabled(true);
    setErrorMessage(r2qConstants('errorMsg'));
    if (allQATests.length !== 0 || error) {
      setAllQATests([]);
      setFetchTicketsEnabled(true);
    }
    setError(false);
  }, [chatId]);

  const openRegenerationModal = (testId: string) => {
    setIsModalOpen(true);
    setRegenerationId(testId);
  };

  const triggerRegeneration = async (userInput: string) => {
    try {
      await axiosInstance.post('/api/r2q/regenerate', {
        ticket_id: regenerationId,
        feedback: userInput,
      });
      setRegeneratingTicketIds((prevState) => ({
        ...prevState,
        [chatId as string]: [...(prevState[chatId as string] || []), regenerationId],
      }));

      setPollingAfterRegeneration(true);

      closeModal();
      setLoading(false);
    } catch (error) {
      setLoading(false);
      log.warn('Error regenerating the ticket:', error);
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const isLikeEnabled = (test: ExtendedTicketData) => {
    return test.liked === null || !test.liked;
  };

  const isDislikeEnabled = (test: ExtendedTicketData) => {
    return test.liked === null || test.liked;
  };

  const isEditEnabled = (test: ExtendedTicketData) => {
    return !test.url && !pollingAfterPublish;
  };

  const isRegenerateEnabled = (test: ExtendedTicketData) => {
    return !test.url && !pollingAfterPublish && !test.regenerated && !test.is_published;
  };

  const csvData = getCsvDataForQATests(qaTestsByStoryId);

  const handleSelectionChange = (test: ExtendedTicketData) => {
    test.isSelected = !test.isSelected;
    setAllQATests([...allQATests]);
  };

  const isSelectionDisabled = (test: ExtendedTicketData) => {
    return !!test.url || publishingTicketIds.includes(test.id) || test.is_published;
  };

  const isSelected = (test: ExtendedTicketData) => {
    return test.isSelected || !!test.url || publishingTicketIds.includes(test.id);
  };

  const handleVersionChange = async (versionId: number, test: ExtendedTicketData) => {
    try {
      const originalTicketId = test?.original_ticket_id;
      setLoadingTicketIds((prev) => [...prev, originalTicketId]);
      const response = await axiosInstance.get('/api/platform/ticket-version', {
        params: {
          versionId: versionId,
          originalTicketId: originalTicketId,
        },
      });

      const oldTest = qaTestsByStoryId[test.story_id].find(
        (currentTest) => currentTest.original_ticket_id === originalTicketId,
      );
      const newTest = response.data;
      newTest.isSelected = oldTest?.isSelected;
      setLoadingTicketIds((prev) => prev.filter((id) => id !== originalTicketId));
      setAllQATests((prev) => {
        const updatedTests = prev.map((currentTest) => {
          if (currentTest.original_ticket_id === originalTicketId) {
            return newTest;
          }
          return currentTest;
        });
        return updatedTests;
      });
    } catch (error) {
      setLoadingTicketIds((prev) => prev.filter((id) => id !== test.original_ticket_id));
      log.warn('Error updating the ticket:', error);
    }
  };

  const renderQATest = (test: ExtendedTicketData) => {
    if (
      (regeneratingTicketIds[chatId as string]?.includes(test.id) &&
        (pollingEnabled || pollingAfterRegeneration)) ||
      loadingTicketIds?.includes(test.original_ticket_id)
    ) {
      return <LoadingSpinner />;
    }

    if (isEditing && editTest?.id === test.id) {
      return (
        <QATestsEdit
          id={test.id}
          title={test?.title}
          description={test?.description}
          preConditions={test?.preconditions ?? ''}
          testType={test?.test_type ?? ''}
          testSteps={test?.test_steps ?? ''}
          expectedResults={test?.expected_results ?? ''}
          setIsEditing={setIsEditing}
        />
      );
    }

    return (
      <div className="flex items-start justify-start">
        <Checkbox
          className="my-1"
          isSelected={isSelected(test)}
          onChange={() => handleSelectionChange(test)}
          isDisabled={isSelectionDisabled(test)}
        />
        <div className="flex flex-col gap-2">
          <p className="label-l text-primary-teal-600">{test?.title}</p>

          <div>
            <p className="label-m text-primary-teal-600">
              {r2qConstants('testFields.description')}
            </p>
            <Markdown content={test?.description || ''} />
          </div>
          <div>
            <p className="label-m text-primary-teal-600">
              {r2qConstants('testFields.preconditions')}
            </p>
            <Markdown content={formatTextWithLineBreaks(test?.preconditions || '')} />
          </div>
          <div>
            <p className="label-m text-primary-teal-600">{r2qConstants('testFields.testType')}</p>
            <Markdown content={test?.test_type || 'TCD'} />
          </div>
          <div>
            <p className="label-m text-primary-teal-600">{r2qConstants('testFields.testSteps')}</p>
            <Markdown content={formatTextWithLineBreaks(test?.test_steps || '')} />
          </div>
          <div>
            <p className="label-m text-primary-teal-600">
              {r2qConstants('testFields.expectedResults')}
            </p>
            <Markdown content={formatTextWithLineBreaks(test?.expected_results || '')} />
          </div>
        </div>
      </div>
    );
  };

  const renderPageContent = () => {
    if (loading && allQATests.length == 0 && !error) {
      return <LoadingSpinner />;
    }
    if (error && allQATests.length == 0) {
      return (
        <div>
          <p className="label-s px-2 text-danger">{errorMessage}</p>
        </div>
      );
    }
    return (
      <div className="flex flex-col gap-4">
        <div className="flex w-full items-center justify-between">
          <p className="label-m text-secondary-neutral-900">{r2qConstants('subHeading')}</p>
          <CSVLink
            data={csvData}
            headers={QA_TESTS_CSV_LABELS}
            filename={getFileNameCSV(SubModules.QA_TESTS)}
            className="paragraph-s flex h-10 w-fit items-center rounded-xl border border-secondary-neutral-200 bg-transparent px-4 text-secondary-neutral-900 hover:bg-primary-teal-600"
          >
            {r2qConstants('export')}
          </CSVLink>
        </div>

        <div className="rounded-xl border bg-white">
          {Object.keys(qaTestsByStoryId)
            .sort()
            .map((storyId, index) => {
              const storyTitle = qaTestsByStoryId[storyId][0]?.story_title;
              const issueId = qaTestsByStoryId[storyId][0]?.story_id;
              const isPublished = qaTestsByStoryId[storyId].every((story) => story.url);

              return (
                <div className="flex flex-col items-center" key={index}>
                  <Accordion key={storyId}>
                    <AccordionItem
                      className="px-4 text-secondary-neutral-900"
                      textValue={`${storyId}`}
                      title={
                        <div className="flex items-center justify-between">
                          <div className={`flex items-center gap-2 ${isPublished ? 'py-1' : ''}`}>
                            <p className="label-m text-primary-teal-600">{issueId}</p>
                            <p className="label-m">{storyTitle}</p>
                          </div>
                          {!isPublished && (
                            <OptionsMenu
                              openRegenerationModal={() => {}}
                              setRegenerationConfig={setRegenerationId}
                              type={SubModules.QA_TESTS}
                              id={storyId}
                              showPublish={true}
                              isPublished={isPublished}
                              areStoriesGenerated={true}
                              showLeftSideOptions={false}
                              qaTestsByStoryId={qaTestsByStoryId}
                            />
                          )}
                        </div>
                      }
                      classNames={{
                        content: 'pb-4',
                      }}
                    >
                      <div
                        key={storyId}
                        className="flex flex-col gap-4 rounded-lg bg-secondary-neutral-50 p-4"
                      >
                        {qaTestsByStoryId[storyId].map(
                          (test: ExtendedTicketData, index: number) => (
                            <div key={test.id} className="flex flex-col gap-4">
                              {renderQATest(test)}
                              {(!regeneratingTicketIds[chatId as string]?.includes(test.id) ||
                                (!pollingEnabled && !pollingAfterRegeneration)) && (
                                <div
                                  className={`${index + 1 === qaTestsByStoryId[storyId]?.length ? 'pb-3' : ''}`}
                                >
                                  <div className="flex items-center justify-between">
                                    <div className="flex-grow">
                                      <OptionsMenu
                                        isLikeEnabled={isLikeEnabled(test)}
                                        isDislikeEnabled={isDislikeEnabled(test)}
                                        openRegenerationModal={() => openRegenerationModal(test.id)}
                                        setRegenerationConfig={setRegenerationId}
                                        type={SubModules.QA_TESTS}
                                        id={test.id}
                                        onEdit={() => {
                                          setIsEditing(true);
                                          setEditTest(test);
                                        }}
                                        url={test.url}
                                        showPublish={!!test.url}
                                        isPublished={Boolean(test.url)}
                                        areStoriesGenerated={true}
                                        isRegenerateEnabled={isRegenerateEnabled(test)}
                                        isEditEnabled={isEditEnabled(test)}
                                        qaTestsByStoryId={qaTestsByStoryId}
                                        isQaTest={true}
                                      />
                                    </div>
                                    <Version
                                      test={test}
                                      handleVersionChange={handleVersionChange}
                                    />
                                  </div>
                                </div>
                              )}

                              {index + 1 !== qaTestsByStoryId[storyId]?.length && <Divider />}
                            </div>
                          ),
                        )}
                      </div>
                    </AccordionItem>
                  </Accordion>

                  {index !== Object.keys(qaTestsByStoryId).length - 1 && (
                    <div className="w-full px-5">
                      <Divider className="w-full bg-gray-300" />
                    </div>
                  )}
                </div>
              );
            })}
        </div>

        <RegenerationModal
          isOpen={isModalOpen}
          triggerRegeneration={triggerRegeneration}
          closeModal={closeModal}
        />
      </div>
    );
  };

  const fetchRegeneratedTickets = async () => {
    try {
      const previousTests = allQATests.filter((test) => previousTicketIds.includes(test.id));
      const newTestsPromises = previousTests.map((test) =>
        axiosInstance.get('/api/platform/ticket-version', {
          params: {
            versionId: test.version + 1,
            originalTicketId: test.original_ticket_id,
          },
        }),
      );
      const response = await Promise.all(newTestsPromises);
      const newTests = response.map((res) => res.data);

      newTests.forEach((newTest) => {
        const previousTest = previousTests.find(
          (test) => test.original_ticket_id === newTest.original_ticket_id,
        );
        newTest.isSelected = previousTest?.isSelected;
      });

      setAllQATests((prev) => {
        const newTestsMap = newTests.reduce((acc, newTest) => {
          acc[newTest.original_ticket_id] = newTest;
          return acc;
        }, {});
        return prev.map((test) => {
          if (newTestsMap[test.original_ticket_id]) {
            return newTestsMap[test.original_ticket_id];
          }
          return test;
        });
      });
    } catch (error) {
      log.warn('Error fetching regenerated tickets:', error);
    }
  };

  return (
    <div className="flex h-full w-full gap-6">
      <ModuleSidebar
        breadcrumbItems={R2Q_BREADCRUMBS.map((item) => {
          return { ...item, children: commonConstants(item.children) };
        })}
        tabItems={tabItems}
        activeRequests={activeRequests}
        archivedRequests={archivedRequests}
      />
      <div className="flex h-full w-full flex-col justify-between gap-6 rounded-lg border p-4">
        <ChatId chatId={chatId as string} />
        <UserMessage message={chatMessage ?? {}} isLoading={isLoading} isError={isError} />

        <div className="h-full overflow-y-scroll rounded-xl bg-secondary-neutral-50 px-4 py-2">
          <div className="flex items-center gap-4 py-3">
            <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
              <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
            </div>
            <p className="label-m text-secondary-neutral-900">{r2qConstants('heading')}</p>
          </div>
          {renderPageContent()}
        </div>
      </div>
    </div>
  );
};

export default Layout(QATestsPageWrapper, LayoutType.MAIN);

QATestsPage.messages = ['R2Q', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale } = context;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    const platformConfigs = await getPlatformConfigs(apiBaseUrl, token);

    return {
      props: {
        envConfig,
        jiraConfig: platformConfigs?.jiraConfig,
        messages: await getMessages(locale ?? 'en', QATestsPage.messages),
      },
    };
  },
);
