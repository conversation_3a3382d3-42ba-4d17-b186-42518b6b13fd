import React from 'react';
import { render, screen } from '@testing-library/react';
import SkeletonLoader from '../index';

describe('SkeletonLoader', () => {
  it('renders with default props', () => {
    render(<SkeletonLoader />);
    const skeletonElements = screen.getAllByRole('generic');
    expect(skeletonElements).toHaveLength(4);
  });

  it('renders with custom number of lines', () => {
    render(<SkeletonLoader lines={5} />);
    const skeletonElements = screen.getAllByRole('generic');
    expect(skeletonElements).toHaveLength(6); 
  });

  it('applies custom className', () => {
    render(<SkeletonLoader className="custom-class" />);
    const container = screen.getAllByRole('generic')[0];
    expect(container).toHaveClass('custom-class');
  });
}); 