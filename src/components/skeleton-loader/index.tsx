import React from 'react';

interface SkeletonLoaderProps {
  lines?: number;
  className?: string;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({ 
  lines = 3, 
  className = "" 
}) => {
  return (
    <div className={`flex flex-col gap-2 ${className}`}>
      {Array.from({ length: lines }, (_, index) => (
        <div
          key={index}
          className={`h-4 bg-gray-200 rounded animate-pulse ${
            index === 0 ? 'w-full' : 
            index === 1 ? 'w-4/5' : 
            'w-3/5'
          }`}
        />
      ))}
    </div>
  );
};

export default SkeletonLoader; 