import React, { forwardRef, ChangeEvent } from 'react';
import { Textarea as NextUITextArea, SlotsToClasses, TextAreaProps } from '@heroui/react';

interface ITextareaProps extends TextAreaProps {
  label?: string;
  placeholder?: string;
  isInvalid?: boolean;
  errorMessage?: string;
  isRequired?: boolean;
  helperText?: string;
  startIcon?: React.ReactNode;
  className?: string;
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  inputBackground?: string;
}

const Textarea = forwardRef<HTMLTextAreaElement, ITextareaProps>(
  (
    {
      label,
      placeholder,
      isInvalid,
      errorMessage,
      isRequired = false,
      helperText,
      startIcon,
      className,
      onChange,
      inputBackground,
      ...rest
    },
    ref,
  ) => {
    const textareaStyles: SlotsToClasses<'inputWrapper' | 'innerWrapper'> = {
      inputWrapper:
        `border-1 border-secondary-neutral-200 group-data-[hover=true]:border-secondary-neutral-300 group-data-[focus=true]:border-secondary-neutral-300 ${inputBackground || ''}`,
      innerWrapper: 'items-center',
    };

    return (
      <NextUITextArea
        ref={ref}
        onChange={onChange}
        label={label}
        labelPlacement="outside"
        variant="bordered"
        aria-label="textarea"
        placeholder={placeholder}
        isInvalid={isInvalid}
        errorMessage={errorMessage}
        isRequired={isRequired}
        description={helperText}
        startContent={startIcon}
        className={className ?? ''}
        classNames={textareaStyles}
        {...rest}
      />
    );
  },
);

Textarea.displayName = 'Textarea';

export default Textarea;
