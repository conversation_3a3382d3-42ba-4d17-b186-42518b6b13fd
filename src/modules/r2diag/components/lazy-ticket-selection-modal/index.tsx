import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useInfiniteQuery } from '@tanstack/react-query';
import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Checkbox from '@/components/checkbox';
import LoadingSpinner from '@/components/loading-spinner';
import { Modal } from '@/components/modal';
import { ModalSize } from '@/components/modal/types';
import Markdown from '@/modules/platform/components/markdown';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import { handleScrollPreserve, restoreScrollPosition } from '@/modules/r2c/utils/scroll';
import { Accordion, AccordionItem } from '@heroui/react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import MagnifyingGlassIcon from '@heroicons/react/24/outline/MagnifyingGlassIcon';
import axiosInstance from '@/utils/axios';
import log from '@/utils/logger';

interface ILazyTicketSelectionModalProps {
  title: string;
  noTicketsError: string;
  maxLimitError: string;
  isOpen: boolean;
  onClose: () => void;
  userSelectedTickets: IJiraTicket[];
  setUserSelectedTickets: React.Dispatch<React.SetStateAction<IJiraTicket[]>>;
  pageSize?: number;
}

const ITEMS_PER_PAGE = 10;
const MAX_SELECTED_TICKETS = 5;

const LazyTicketSelectionModal = ({
  title,
  noTicketsError,
  maxLimitError,
  isOpen,
  onClose,
  userSelectedTickets,
  setUserSelectedTickets,
  pageSize = ITEMS_PER_PAGE,
}: ILazyTicketSelectionModalProps) => {
  const [error, setError] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>('');
  const containerRef = useRef<HTMLDivElement>(null);

  const { control, handleSubmit, watch, setValue } = useForm<{
    selectedTickets: IJiraTicket[];
  }>();

  const selectedTickets = watch('selectedTickets', []);

  const updateSelectedTickets = useCallback(
    (newTickets: IJiraTicket[]) => {
      if (newTickets.length > MAX_SELECTED_TICKETS) {
        const truncatedTickets = newTickets.slice(0, MAX_SELECTED_TICKETS);
        setValue('selectedTickets', truncatedTickets);
        setError(maxLimitError);
        return false;
      }

      setValue('selectedTickets', newTickets);
      setError('');
      return true;
    },
    [setValue, maxLimitError],
  );

  const latestSelectionRef = useRef<IJiraTicket[]>([]);

  useEffect(() => {
    latestSelectionRef.current = selectedTickets;
  }, [selectedTickets]);

  const updateTicketSelection = useCallback(
    (ticket: IJiraTicket, checked: boolean) => {
      const currentTickets = latestSelectionRef.current;
      let newSelection: IJiraTicket[];

      if (checked) {
        if (currentTickets.some((t) => t.ticketId === ticket.ticketId)) {
          return;
        }
        if (currentTickets.length >= MAX_SELECTED_TICKETS) {
          setError(maxLimitError);
          return;
        }
        newSelection = [...currentTickets, ticket];
      } else {
        newSelection = currentTickets.filter((t) => t.ticketId !== ticket.ticketId);
      }

      latestSelectionRef.current = newSelection;
      updateSelectedTickets(newSelection);
    },
    [maxLimitError, updateSelectedTickets],
  );

  const [allTicketsFromAPI, setAllTicketsFromAPI] = useState<IJiraTicket[]>([]);
  const [hasLoadedInitialData, setHasLoadedInitialData] = useState(false);
  const [isLoadingInitialData, setIsLoadingInitialData] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const initialDataPromiseRef = useRef<Promise<IJiraTicket[]> | null>(null);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      initialDataPromiseRef.current = null;
    };
  }, []);

  useEffect(() => {
    if (!isOpen) {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      setIsLoadingInitialData(false);
      initialDataPromiseRef.current = null;
    } else {
      setHasLoadedInitialData(false);
      setAllTicketsFromAPI([]);
    }
  }, [isOpen]);

  const fetchAllTickets = async (searchTerm?: string) => {
    try {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const params: Record<string, string> = {};

      if (searchTerm) {
        params.search = searchTerm;
      }

      const response = await axiosInstance.get('/api/platform/jira', {
        params,
        signal: abortController.signal,
      });

      const tickets = response.data || [];
      setAllTicketsFromAPI(tickets);
      setHasLoadedInitialData(true);
      setIsLoadingInitialData(false);
      return tickets;
    } catch (err) {
      if (err instanceof Error && err.name === 'CanceledError') {
        setIsLoadingInitialData(false);
        throw err;
      }

      log.error('Error fetching tickets:', err);
      setHasLoadedInitialData(true);
      setIsLoadingInitialData(false);
      throw err;
    }
  };

  const fetchTickets = async ({ pageParam = 1 }) => {
    let tickets = allTicketsFromAPI;

    if (!hasLoadedInitialData) {
      if (isLoadingInitialData && initialDataPromiseRef.current) {
        tickets = await initialDataPromiseRef.current;
      } else if (!isLoadingInitialData) {
        setIsLoadingInitialData(true);
        const promise = fetchAllTickets();
        initialDataPromiseRef.current = promise;
        tickets = await promise;
        initialDataPromiseRef.current = null;
      }
    }

    const startIndex = (pageParam - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageTickets = tickets.slice(startIndex, endIndex);

    return {
      data: pageTickets,
      nextPage: endIndex < tickets.length ? pageParam + 1 : undefined,
      hasMore: endIndex < tickets.length,
    };
  };

  const { data, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } = useInfiniteQuery({
    queryKey: ['lazyTickets', { pageSize }],
    queryFn: fetchTickets,
    enabled: isOpen && !debouncedSearchQuery,
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
    staleTime: 2 * 60 * 1000,
  });

  const { data: searchData, isLoading: isSearchLoading } = useInfiniteQuery({
    queryKey: ['searchTickets', { searchTerm: debouncedSearchQuery }],
    queryFn: async () => {
      try {
        const tickets = await fetchAllTickets(debouncedSearchQuery);
        return {
          data: tickets,
          nextPage: undefined,
          hasMore: false,
        };
      } catch (err) {
        if (err instanceof Error && err.name === 'CanceledError') {
          return {
            data: [],
            nextPage: undefined,
            hasMore: false,
          };
        }
        throw err;
      }
    },
    enabled: isOpen && !!debouncedSearchQuery,
    getNextPageParam: () => undefined,
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000,
  });

  const isSearching = !!debouncedSearchQuery;
  const currentData = isSearching ? searchData : data;
  const currentIsLoading = isSearching ? isSearchLoading : isLoading;

  const allTickets = currentData?.pages.flatMap((page) => page.data || []) || [];

  const filteredTickets = isSearching
    ? allTickets.filter((ticket) => {
        if (!ticket || typeof ticket !== 'object') {
          return false;
        }

        if (!ticket.title || !ticket.ticketId) {
          return false;
        }

        const searchTerm = debouncedSearchQuery.toLowerCase();
        return (
          ticket.title.toLowerCase().includes(searchTerm) ||
          ticket.ticketId.toLowerCase().includes(searchTerm)
        );
      })
    : allTickets.filter((ticket) => {
        return ticket && typeof ticket === 'object' && ticket.title && ticket.ticketId;
      });
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 50;

      if (isNearBottom && hasNextPage && !isFetchingNextPage && !isSearching) {
        fetchNextPage();
      }
    };

    container.addEventListener('scroll', handleScroll);

    handleScroll();

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [hasNextPage, isFetchingNextPage, searchQuery, fetchNextPage]);

  const handleSelectionChange = useCallback(
    (ticket: IJiraTicket, checked: boolean) => {
      const scrollPosition = handleScrollPreserve(containerRef);

      updateTicketSelection(ticket, checked);

      requestAnimationFrame(() => restoreScrollPosition(scrollPosition, containerRef));
    },
    [updateTicketSelection],
  );

  const handleClose = () => {
    updateSelectedTickets(userSelectedTickets);
    onClose();
  };

  const onSubmit = (data: { selectedTickets: IJiraTicket[] }) => {
    if (data.selectedTickets.length > MAX_SELECTED_TICKETS) {
      setError(maxLimitError);
      return;
    }

    setUserSelectedTickets(data.selectedTickets);
    onClose();
  };

  useEffect(() => {
    updateSelectedTickets(userSelectedTickets);
  }, [userSelectedTickets, updateSelectedTickets]);

  useEffect(() => {
    setError('');
  }, [isOpen]);

  const ticketSelectionConstants = useTranslations('Platform.ticketSelectionModal');

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      bodyContent={
        <div
          ref={containerRef}
          className="flex h-fit w-full flex-col items-start gap-6 overflow-y-auto rounded-lg bg-white p-4"
        >
          <p className="label-m">{title}</p>
          {currentIsLoading ? (
            <LoadingSpinner />
          ) : !allTickets?.length ? (
            <p className="label-s text-secondary-neutral-600">{noTicketsError}</p>
          ) : (
            <>
              <Input
                type={InputType.TEXT}
                placeholder={ticketSelectionConstants('searchTickets')}
                value={searchQuery}
                endContent={<MagnifyingGlassIcon className="h-6 w-6" />}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="mb-4"
              />
              <div ref={scrollContainerRef} className="max-h-64 w-full overflow-y-auto">
                <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-4">
                  {filteredTickets.map((ticket) => (
                    <div key={ticket.ticketId} className="rounded-lg border px-4">
                      <div className="flex space-x-2">
                        <Controller
                          name="selectedTickets"
                          control={control}
                          defaultValue={[]}
                          render={({ field }) => (
                            <Checkbox
                              isSelected={field.value.some(
                                (t: IJiraTicket) => t.ticketId === ticket.ticketId,
                              )}
                              onChange={(e) => handleSelectionChange(ticket, e.target.checked)}
                            />
                          )}
                        />
                        <Accordion selectionMode="multiple">
                          <AccordionItem
                            textValue={`${ticket.ticketId}: ${ticket.title}`}
                            title={
                              <div className="flex justify-between">
                                <span className="flex gap-2">
                                  <p className="label-s whitespace-nowrap text-primary-teal-600">
                                    {ticket.ticketId}:
                                  </p>
                                  <p className="label-s line-clamp-1 flex-grow break-words">
                                    {ticket.title}
                                  </p>
                                </span>
                                {ticket.url && (
                                  <Link href={ticket.url} target="_blank">
                                    <Image
                                      src="/icons/jira.svg"
                                      alt="jira logo"
                                      width={20}
                                      height={20}
                                    />
                                  </Link>
                                )}
                              </div>
                            }
                          >
                            <div className="line-clamp-3">
                              <Markdown content={ticket.description} />
                            </div>
                          </AccordionItem>
                        </Accordion>
                      </div>
                    </div>
                  ))}

                  {/* Loading indicator when fetching more */}
                  {!isSearching && isFetchingNextPage && (
                    <div className="flex justify-center py-2">
                      <LoadingSpinner />
                    </div>
                  )}
                </form>
              </div>
            </>
          )}
        </div>
      }
      footerContent={
        <div className="flex w-full flex-col">
          {error && (
            <div className="mb-3 flex w-full justify-center">
              <p className="label-s text-danger">{error}</p>
            </div>
          )}
          <div className="flex justify-center gap-4">
            <Button
              type={ButtonType.SUBMIT}
              onPress={() => handleSubmit(onSubmit)()}
              isDisabled={selectedTickets.length === 0}
            >
              {ticketSelectionConstants('submitButton')}
            </Button>
            <Button variant={ButtonVariant.BORDERED} onPress={handleClose}>
              {ticketSelectionConstants('cancelButton')}
            </Button>
          </div>
        </div>
      }
      size={ModalSize['3XL']}
    />
  );
};

export default LazyTicketSelectionModal;
