import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import SelectInput, { createSelectInputItem } from '@/components/select';
import Textarea from '@/components/textarea';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import { MinusCircleIcon } from '@heroicons/react/24/outline';
import { Divider, useDisclosure } from '@heroui/react';
import { AxiosError } from 'axios';
import { useTranslations } from 'next-intl';
import React, { useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import TicketSelectionModal from '@/modules/platform/components/ticket-selection-modal';
import { DIAGRAM_TYPES } from '../../constants/inputs';
import { useUserConfigContext } from '@/modules/platform/contexts/config.context';
import { useRouter } from 'next/router';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { useTicketsContext } from '@/modules/platform/contexts/tickets.context';
import { useQuery } from '@tanstack/react-query';
import log from '@/utils/logger';

interface IFormInputs {
  title: string;
  diagramTypes: string;
  technicalSpecifications: string;
}

const R2DiagInputForm = () => {
  const { jiraConfig } = useUserConfigContext();
  const router = useRouter();

  const { setPmpTickets, setPmpEpics, setPmpStories, pmpEpics, pmpStories } = useTicketsContext();

  const [selectedOption, setSelectedOption] = useState<'epics' | 'user-stories'>('epics');
  const [allAIReadyEpics, setAllAIReadyEpics] = useState<IJiraTicket[]>([]);
  const [allAIReadyStories, setAllAIReadyStories] = useState<IJiraTicket[]>([]);
  const [selectedEpics, setSelectedEpics] = useState<IJiraTicket[]>([]);
  const [selectedStories, setSelectedStories] = useState<IJiraTicket[]>([]);
  const [disableSubmit, setDisableSubmit] = useState(false);

  const { isOpen, onOpen, onClose } = useDisclosure();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<IFormInputs>({
    defaultValues: {
      title: '',
      diagramTypes: '',
      technicalSpecifications: '',
    },
  });

  const inputFormConstants = useTranslations('R2Diag');

  const userSelectedTickets = useMemo(
    () => [...selectedEpics, ...selectedStories],
    [selectedEpics, selectedStories],
  );

  const fetchTickets = async () => {
    try {
      const response = await axiosInstance.post('/api/platform/jira', { labels: [] });
      const data = response.data || [];

      setPmpTickets(data);

      const epics = data.filter(
        (t: IJiraTicket) => t.type.toLowerCase() === SubModules.EPICS.toLowerCase(),
      );
      const stories = data.filter(
        (t: IJiraTicket) => t.type.toLowerCase() === SubModules.USER_STORIES.toLowerCase(),
      );

      setPmpEpics(epics);
      setPmpStories(stories);
      setAllAIReadyEpics(epics);
      setAllAIReadyStories(stories);

      return data;
    } catch (err) {
      log.warn('Error fetching tickets', err);
      setAllAIReadyEpics([]);
      setAllAIReadyStories([]);
      return [];
    }
  };

  const { isLoading } = useQuery({
    queryKey: ['readyTickets'],
    queryFn: fetchTickets,
    enabled: !pmpStories.length || !pmpEpics.length,
  });

  useEffect(() => {
    if (pmpStories.length) setAllAIReadyStories(pmpStories);
    if (pmpEpics.length) setAllAIReadyEpics(pmpEpics);
  }, [pmpStories, pmpEpics]);

  const fetchTicketsLazy = async (searchTerm?: string, page: number = 1) => {
    try {
      const pageSize = 10;
      let allTickets = selectedOption === 'epics' ? allAIReadyEpics : allAIReadyStories;

      if (searchTerm && searchTerm.trim()) {
        const searchLower = searchTerm.toLowerCase().trim();
        allTickets = allTickets.filter(
          (ticket: IJiraTicket) =>
            ticket.ticketId.toLowerCase().includes(searchLower) ||
            ticket.title.toLowerCase().includes(searchLower) ||
            ticket.description.toLowerCase().includes(searchLower),
        );
      }

      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageData = allTickets.slice(startIndex, endIndex);

      return {
        data: pageData,
        hasMore: endIndex < allTickets.length,
      };
    } catch (err) {
      log.warn('Error fetching tickets for lazy loading', err);
      return { data: [], hasMore: false };
    }
  };

  const handleRemoveTicket = (ticketId: string) => {
    if (selectedOption === 'epics') {
      setSelectedEpics((prev) => prev.filter((item) => item.ticketId !== ticketId));
    } else {
      setSelectedStories((prev) => prev.filter((item) => item.ticketId !== ticketId));
    }
  };

  const onSubmit = async (data: IFormInputs) => {
    if (disableSubmit) return;

    try {
      setDisableSubmit(true);
      const payload = {
        config_id: jiraConfig?.id,
        title: data.title,
        is_diagram_generation: true,
        is_document_generation: false,
        diagram_types: data.diagramTypes ? data.diagramTypes.split(',') : [],
        selected_tickets: userSelectedTickets.map((ticket) => ({
          ticket_id: ticket.ticketId,
          ticket_title: ticket.title,
        })),
        user_prompt: data.technicalSpecifications,
      };

      const response = await axiosInstance.post('/api/r2diag/generate', payload);
      if (response.data?.chat_id) {
        router.push(`${router.pathname}/${response.data.chat_id}`);
      }
    } catch (error) {
      setDisableSubmit(false);
      if (error instanceof AxiosError && error.response) {
        const responseData = error.response.data;
        if (responseData?.error?.type === 'EITHER_EPICS_OR_TECHNICAL_SPECIFICATIONS_REQUIRED') {
          showToast(ToastType.ERROR, responseData.error.error);
          return;
        }
      }
      showToast(ToastType.ERROR, inputFormConstants('inputs.defaultError'));
    }
  };

  return (
    <form
      className="flex h-full w-full flex-col overflow-y-auto rounded-lg border p-6 text-secondary-neutral-900"
      onSubmit={handleSubmit(onSubmit)}
    >
      <div className="flex flex-col gap-6">
        <p className="label-m">{inputFormConstants('title')}</p>
        <div className="w-1/2">
          <Controller
            name="title"
            control={control}
            rules={{ required: inputFormConstants('inputs.title.error') }}
            render={({ field }) => (
              <Input
                {...field}
                type={InputType.TEXT}
                label={inputFormConstants('inputs.title.label')}
                placeholder={inputFormConstants('inputs.title.placeholder')}
                isRequired
                isInvalid={!!errors.title}
                errorMessage={errors.title?.message}
              />
            )}
          />
        </div>

        <div className="w-1/2">
          <Controller
            name="diagramTypes"
            control={control}
            rules={{ required: inputFormConstants('inputs.diagramTypes.error') }}
            render={({ field }) => (
              <SelectInput
                {...field}
                label={inputFormConstants('inputs.diagramTypes.label')}
                placeholder={inputFormConstants('inputs.diagramTypes.placeholder')}
                className="max-w-xs"
                isRequired
                selectionMode="multiple"
                isInvalid={!!errors.diagramTypes}
                errorMessage={errors.diagramTypes?.message}
              >
                {DIAGRAM_TYPES.map((item) =>
                  createSelectInputItem({ ...item, label: inputFormConstants(item.label) }),
                )}
              </SelectInput>
            )}
          />
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-6">
            {userSelectedTickets.length === 0 ? (
              <p className="label-s">{inputFormConstants('inputs.epics.noEpicsSelected')}</p>
            ) : (
              <p className="label-s">
                {userSelectedTickets.length} {inputFormConstants('inputs.epics.ticketSelected')}
              </p>
            )}
            <Button
              variant={ButtonVariant.FLAT}
              className="h-8 w-fit items-center rounded-xl border bg-white px-3"
              onPress={onOpen}
            >
              {inputFormConstants('inputs.epics.addTicketsButton')}
            </Button>
          </div>
          <TicketSelectionModal
            title={inputFormConstants('inputs.epics.modalTitle')}
            noTicketsError={inputFormConstants('inputs.epics.modalNoTicketsFound')}
            maxLimitError={inputFormConstants('inputs.epics.modalTicketsError')}
            isOpen={isOpen}
            onClose={onClose}
            setSelectedOption={setSelectedOption}
            selectedOption={selectedOption}
            allAIReadyEpics={allAIReadyEpics}
            allAIReadyStories={allAIReadyStories}
            selectedEpics={selectedEpics}
            selectedStories={selectedStories}
            setSelectedEpics={setSelectedEpics}
            setSelectedStories={setSelectedStories}
            isLoading={isLoading}
            enableLazyLoading={true}
            fetchTickets={fetchTicketsLazy}
            pageSize={10}
          />

          {userSelectedTickets.length > 0 && (
            <div className="flex w-fit flex-col gap-2 rounded-lg border border-secondary-neutral-200 p-4">
              {userSelectedTickets.map((ticket) => (
                <div key={ticket.ticketId} className="flex items-center justify-between gap-4">
                  <div className="flex gap-2">
                    <p className="paragraph-s text-primary-teal-600">{ticket.ticketId} :</p>
                    <p className="paragraph-s">{ticket.title}</p>
                  </div>
                  <MinusCircleIcon
                    className="h-6 w-6 cursor-pointer"
                    onClick={() => handleRemoveTicket(ticket.ticketId)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        <Controller
          name="technicalSpecifications"
          control={control}
          render={({ field }) => (
            <Textarea
              {...field}
              label={inputFormConstants('inputs.technicalSpecifications.label')}
              placeholder={inputFormConstants('inputs.technicalSpecifications.placeholder')}
            />
          )}
        />
      </div>

      <Divider className="my-12" />

      <Button type={ButtonType.SUBMIT} className="w-fit" isDisabled={disableSubmit}>
        {inputFormConstants('inputs.generateButton')}
      </Button>
    </form>
  );
};

export default R2DiagInputForm;
