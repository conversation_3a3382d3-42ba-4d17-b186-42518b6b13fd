import React, { useState, useRef } from 'react';
import Button from '@/components/button';
import { ButtonType, ButtonVariant } from '@/components/button/types';
import Checkbox from '@/components/checkbox';
import LoadingSpinner from '@/components/loading-spinner';
import { Modal } from '@/components/modal';
import { ModalSize } from '@/components/modal/types';
import Markdown from '@/modules/platform/components/markdown';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import { handleScrollPreserve, restoreScrollPosition } from '@/modules/r2c/utils/scroll';
import { Accordion, AccordionItem } from '@heroui/react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import Dropdown from '@/components/dropdown';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import MagnifyingGlassIcon from '@heroicons/react/24/outline/MagnifyingGlassIcon';

interface ITicketSelectionModalProps {
  title: string;
  noTicketsError: string;
  maxLimitError: string;
  isOpen: boolean;
  selectedOption?: string;
  onClose: () => void;
  allAIReadyStories: IJiraTicket[];
  allAIReadyEpics?: IJiraTicket[];
  selectedEpics?: IJiraTicket[];
  selectedStories: IJiraTicket[];
  setSelectedEpics?: React.Dispatch<React.SetStateAction<IJiraTicket[]>>;
  setSelectedStories: React.Dispatch<React.SetStateAction<IJiraTicket[]>>;
  isLoading: boolean;
  setSelectedOption?: React.Dispatch<React.SetStateAction<'epics' | 'user-stories'>>;
}

interface Props {
  title: string;
  noTicketsError: string;
  maxLimitError: string;
  isLoading: boolean;
  selectedOption?: string;
  setSelectedOption?: React.Dispatch<React.SetStateAction<'epics' | 'user-stories'>>;
  allAIReadyEpics?: IJiraTicket[];
  allAIReadyStories: IJiraTicket[];
  tempSelectedItems: IJiraTicket[];
  setTempSelectedItems: React.Dispatch<React.SetStateAction<IJiraTicket[]>>;
  error: string;
  setError: (err: string) => void;
}

const ModalBody = ({
  title,
  noTicketsError,
  maxLimitError,
  isLoading,
  selectedOption = 'user-stories',
  setSelectedOption,
  allAIReadyEpics = [],
  allAIReadyStories,
  tempSelectedItems,
  setTempSelectedItems,
  setError,
}: Props) => {
  const ticketSelectionConstants = useTranslations('Platform.ticketSelectionModal');
  const [searchQuery, setSearchQuery] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);

  const tickets = selectedOption === 'epics' ? allAIReadyEpics : allAIReadyStories;

  const filteredTickets = tickets.filter(
    (ticket) =>
      ticket.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.ticketId.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleSelectionChange = (ticket: IJiraTicket) => {
    const scrollPos = handleScrollPreserve(containerRef);
    setTempSelectedItems((prev) => {
      const already = prev.some((t) => t.ticketId === ticket.ticketId);
      if (already) {
        return prev.filter((t) => t.ticketId !== ticket.ticketId);
      } else {
        if (prev.length >= 5) {
          setError(maxLimitError);
          return prev;
        }
        setError('');
        return [...prev, ticket];
      }
    });
    setTimeout(() => restoreScrollPosition(scrollPos, containerRef), 0);
  };

  return (
    <div ref={containerRef} className="flex flex-col gap-6 overflow-y-auto bg-white p-4">
      <p className="label-m">{title}</p>

      {isLoading ? (
        <LoadingSpinner />
      ) : !allAIReadyEpics.length && !allAIReadyStories.length ? (
        <p className="label-s text-secondary-neutral-600">{noTicketsError}</p>
      ) : (
        <>
          <Dropdown
            triggerComponent={
              <div className="w-full rounded-lg border border-gray-300 px-4 py-2">
                <p>
                  {selectedOption === 'epics'
                    ? ticketSelectionConstants('epics')
                    : ticketSelectionConstants('stories')}
                </p>
              </div>
            }
            options={[
              {
                text: ticketSelectionConstants('epics'),
                onClick: () => setSelectedOption?.('epics'),
              },
              {
                text: ticketSelectionConstants('stories'),
                onClick: () => setSelectedOption?.('user-stories'),
              },
            ]}
          />

          <Input
            type={InputType.TEXT}
            placeholder={ticketSelectionConstants('searchTickets')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            endContent={<MagnifyingGlassIcon className="h-6 w-6" />}
            className="mb-4"
          />

          <div className="flex flex-col gap-4">
            {filteredTickets.map((ticket: IJiraTicket) => (
              <div key={ticket.ticketId} className="rounded-lg border px-4">
                <div className="flex items-center gap-2">
                  <Checkbox
                    isSelected={tempSelectedItems.some(
                      (t: IJiraTicket) => t.ticketId === ticket.ticketId,
                    )}
                    onChange={() => handleSelectionChange(ticket)}
                  />
                  <Accordion selectionMode="multiple" className="flex-1">
                    <AccordionItem
                      textValue={`${ticket.ticketId}: ${ticket.title}`}
                      title={
                        <div className="flex items-center justify-between">
                          <span className="flex gap-2">
                            <p className="label-s text-primary-teal-600">{ticket.ticketId}:</p>
                            <p className="label-s break-words">{ticket.title}</p>
                          </span>
                          {ticket.url && (
                            <Link href={ticket.url} target="_blank">
                              <Image src="/icons/jira.svg" alt="jira logo" width={20} height={20} />
                            </Link>
                          )}
                        </div>
                      }
                    >
                      <Markdown content={ticket.description} />
                    </AccordionItem>
                  </Accordion>
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

const ModalFooter = ({
  isLoading,
  error,
  tempSelectedItems,
  onConfirm,
  onCancel,
}: {
  isLoading: boolean;
  error: string;
  tempSelectedItems: IJiraTicket[];
  onConfirm: () => void;
  onCancel: () => void;
}) => {
  const t = useTranslations('Platform.ticketSelectionModal');

  return (
    <div className="flex w-full flex-col items-center gap-3">
      {error && <p className="label-s text-danger">{error}</p>}
      <div className="flex justify-center gap-4">
        <Button
          type={ButtonType.SUBMIT}
          onClick={onConfirm}
          isDisabled={isLoading || tempSelectedItems.length === 0}
        >
          {t('submitButton')}
        </Button>
        <Button variant={ButtonVariant.BORDERED} onClick={onCancel}>
          {t('cancelButton')}
        </Button>
      </div>
    </div>
  );
};

const TicketSelectionModal = ({
  title,
  noTicketsError,
  maxLimitError,
  isOpen,
  onClose,
  selectedOption,
  setSelectedOption,
  setSelectedEpics,
  setSelectedStories,
  allAIReadyEpics,
  allAIReadyStories,
  isLoading,
}: ITicketSelectionModalProps) => {
  const [tempSelectedItems, setTempSelectedItems] = useState<IJiraTicket[]>([]);
  const [error, setError] = useState('');
  const handleConfirm = () => {
    if (selectedOption === 'epics') {
      if (setSelectedEpics) {
        setSelectedEpics(tempSelectedItems);
      }
      setSelectedStories([]);
    } else {
      setSelectedStories(tempSelectedItems);
      if (setSelectedEpics) {
        setSelectedEpics([]);
      }
    }
    onClose();
  };

  const handleCancel = () => {
    setTempSelectedItems([]);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size={ModalSize['3XL']}
      bodyContent={
        <ModalBody
          title={title}
          noTicketsError={noTicketsError}
          maxLimitError={maxLimitError}
          selectedOption={selectedOption}
          setSelectedOption={setSelectedOption}
          allAIReadyEpics={allAIReadyEpics}
          allAIReadyStories={allAIReadyStories}
          tempSelectedItems={tempSelectedItems}
          setTempSelectedItems={setTempSelectedItems}
          error={error}
          setError={setError}
          isLoading={isLoading}
        />
      }
      footerContent={
        <ModalFooter
          error={error}
          isLoading={isLoading}
          tempSelectedItems={tempSelectedItems}
          onConfirm={handleConfirm}
          onCancel={handleCancel}
        />
      }
    />
  );
};

export default TicketSelectionModal;
