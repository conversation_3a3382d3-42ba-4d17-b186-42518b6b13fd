import { Modules, SubModules } from '@/modules/platform/interfaces/modules';

export enum ChatType {
  AI = 'AI',
  HUMAN = 'HUMAN',
}

export enum Intent {
  CREATE_PLAN = 'CREATE_PLAN',
  GENERATE_CODE = 'GENERATE_CODE',
  OTHER = 'OTHER',
}

export interface IChatMessage {
  id: string;
  messageType: string;
  displayMessage: string;
  intent: Intent;
  subType?: string;
}

export interface IChatMessageResponse {
  id: string;
  chat_id: string;
  type: Modules;
  sub_type: SubModules;
  message: string;
  display_message: string;
  intent: Intent;
  message_type: ChatType;
  owner_id: string;
  liked: boolean;
  regenerated: boolean;
  created_at: string;
  updated_at: string;
}
