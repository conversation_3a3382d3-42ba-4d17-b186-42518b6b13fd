import { IJiraTicket, TicketCollections } from '@/modules/platform/interfaces/tickets';
import React, { useEffect, useRef, useState } from 'react';
import { Modal } from '@/components/modal';
import { Modules, SubModules } from '@/modules/platform/interfaces/modules';
import { useTranslations } from 'next-intl';
import { ButtonVariant } from '@/components/button/types';
import Dropdown from '@/components/dropdown';
import { ModalSize } from '@/components/modal/types';
import LoadingSpinner from '@/components/loading-spinner';
import Input from '@/components/input';
import { InputType } from '@/components/input/types';
import Checkbox from '@/components/checkbox';
import { Accordion, AccordionItem, Divider } from '@heroui/react';
import Markdown from '@/modules/platform/components/markdown';
import { ConfigTypes } from '@/modules/platform/interfaces/config';
import router from 'next/router';
import { useTicketsContext } from '@/modules/platform/contexts/tickets.context';
import { useQuery } from '@tanstack/react-query';
import { SelectedTickets } from '../selected-tickets';
import Button from '@/components/button';
import { TicketLabels } from '@/modules/platform/interfaces/tickets';
import Textarea from '@/components/textarea';
import log from '@/utils/logger';
import Link from 'next/link';
import Image from 'next/image';
import axiosInstance from '@/utils/axios';
import { handleScrollPreserve, restoreScrollPosition } from '@/modules/r2c/utils/scroll';
import showToast, { ToastType } from '@/utils/toast';
import { AxiosError } from 'axios';
import MagnifyingGlassIcon from '@heroicons/react/24/outline/MagnifyingGlassIcon';
import MinusCircleIcon from '@heroicons/react/24/outline/MinusCircleIcon';

import {
  getTicketTypeFromOption,
  TICKET_TYPE_TO_SUBMODULE_MAP,
  type TicketCollectionKey,
} from '../../mappers/ticket-type-mapper';

const QaTestsGenerationForm = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [allAIReady, setAllAIReady] = useState<TicketCollections>({
    epics: [],
    stories: [],
    tasks: [],
    bugs: [],
    subBugs: [],
    subTasks: [],
  });

  const [selectedTickets, setSelectedTickets] = useState<TicketCollections>({
    epics: [],
    stories: [],
    tasks: [],
    bugs: [],
    subBugs: [],
    subTasks: [],
  });

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState<string>('epics');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [tempSelectedItems, setTempSelectedItems] = useState<IJiraTicket[]>([]);
  const [additionalInput, setAdditionalInput] = useState<string>('');

  const [title, setTitle] = useState<string>('');
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [gitlabLink, setGitlabLink] = useState<string>('');

  const [additionalGitlabLinks, setAdditionalGitlabLinks] = useState<string[]>([]);
  const [showGitlabValidation, setShowGitlabValidation] = useState<boolean>(false);
  const {
    setPmpTickets,
    setPmpEpics,
    pmpStories,
    setPmpStories,
    pmpEpics,
    pmpTasks,
    setPmpTasks,
    pmpBugs,
    setPmpBugs,
    pmpSubBugs,
    setPmpSubBugs,
    pmpSubTasks,
    setPmpSubTasks,
  } = useTicketsContext();
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  const updateSelectedTickets = (ticketType: keyof TicketCollections, tickets: IJiraTicket[]) => {
    setSelectedTickets(prev => ({
      ...prev,
      [ticketType]: tickets
    }));
  };

  const getAllAIReadyMap = (): Record<string, IJiraTicket[]> => ({
    'epics': allAIReady.epics,
    'user-stories': allAIReady.stories,
    'tasks': allAIReady.tasks,
    'bugs': allAIReady.bugs,
    'sub-bugs': allAIReady.subBugs,
    'sub-tasks': allAIReady.subTasks,
  });

  const modalContainerRef = React.useRef<HTMLDivElement>(null);

  const ticketTypeConfig = [
    { key: 'stories' as keyof TicketCollections, subModule: SubModules.USER_STORIES, setter: setPmpStories },
    { key: 'epics' as keyof TicketCollections, subModule: SubModules.EPICS, setter: setPmpEpics },
    { key: 'tasks' as keyof TicketCollections, subModule: SubModules.TASKS, setter: setPmpTasks },
    { key: 'bugs' as keyof TicketCollections, subModule: SubModules.BUGS, setter: setPmpBugs },
    { key: 'subBugs' as keyof TicketCollections, subModule: SubModules.SUB_BUGS, setter: setPmpSubBugs },
    { key: 'subTasks' as keyof TicketCollections, subModule: SubModules.SUB_TASKS, setter: setPmpSubTasks },
  ];

  const createEmptyTicketState = (): TicketCollections => {
    return ticketTypeConfig.reduce((acc, { key }) => {
      acc[key] = [];
      return acc;
    }, {} as TicketCollections);
  };

  const normalizeTicketType = (ticketType: string): string => {
    return ticketType.toLowerCase().replace(/-/g, '_');
  };

  const fetchAIReadyTickets = async () => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.post('/api/platform/jira', {
        labels: [TicketLabels.QA_READY],
      });

      if (response.data) {
        setPmpTickets(response.data);

        const filteredTickets = ticketTypeConfig.reduce((acc, { key, subModule, setter }) => {
          const filtered = response.data.filter(
            (ticket: IJiraTicket) =>
              normalizeTicketType(ticket.type) === normalizeTicketType(subModule)
          );
          acc[key] = filtered;
          setter(filtered);
          return acc;
        }, {} as TicketCollections);

        setAllAIReady(filteredTickets);
      }

      setIsLoading(false);
      return response.data;
    } catch (error) {
      setIsLoading(false);
      log.warn('Error in fetching jira tickets data', error);

      setAllAIReady(createEmptyTicketState());
      return [];
    }
  };

  useQuery({
    queryKey: ['aiReadyTickets'],
    queryFn: () => fetchAIReadyTickets(),
    enabled: !pmpStories.length || !pmpEpics.length,
  });

  useEffect(() => {
    setAllAIReady(prev => ({
      ...prev,
      ...(pmpStories.length && { stories: pmpStories }),
      ...(pmpEpics.length && { epics: pmpEpics }),
      ...(pmpTasks.length && { tasks: pmpTasks }),
      ...(pmpBugs.length && { bugs: pmpBugs }),
      ...(pmpSubBugs.length && { subBugs: pmpSubBugs }),
      ...(pmpSubTasks.length && { subTasks: pmpSubTasks }),
    }));
  }, [pmpStories, pmpEpics, pmpTasks, pmpBugs, pmpSubBugs, pmpSubTasks]);

  const handleSelectionChange = (ticket: IJiraTicket) => {
    const scrollPosition = handleScrollPreserve(modalContainerRef);
    setTimeout(() => restoreScrollPosition(scrollPosition, modalContainerRef), 0);

    setTempSelectedItems((prev) =>
      prev.some((item) => item.ticketId === ticket.ticketId)
        ? prev.filter((item) => item.ticketId !== ticket.ticketId)
        : [...prev, ticket],
    );
  };

  const removeSelectedTicket = (ticketId: string) => {
    const ticketType = getTicketTypeFromOption(selectedOption);
    if (ticketType) {
      updateSelectedTickets(
        ticketType,
        selectedTickets[ticketType].filter((item) => item.ticketId !== ticketId)
      );
    }
  };

  const openModal = () => {
    setIsModalOpen(true);
    const ticketType = getTicketTypeFromOption(selectedOption);
    if (ticketType) {
      setTempSelectedItems(selectedTickets[ticketType]);
    }
  };

  useEffect(() => {
    setSearchQuery('');
  }, [selectedOption]);

  const closeModal = () => {
    setTempSelectedItems([]);
    setSearchQuery('');
    setIsModalOpen(false);
  };

  const confirmSelection = () => {
    const ticketType = getTicketTypeFromOption(selectedOption);
    if (ticketType) {
      const emptyAll = createEmptyTicketState();
      setSelectedTickets({
        ...emptyAll,
        [ticketType]: tempSelectedItems
      });
    }

    setIsModalOpen(false);
  };

  const filteredTickets = (() => {
    const tickets = getAllAIReadyMap()[selectedOption] || [];

    return tickets.filter(
      (ticket) =>
        ticket.ticketId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.title.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  })();

  const renderTicketsList = () => {
    return (
      <div className="flex flex-col gap-y-4">
        {filteredTickets.map((ticket) => (
          <div className="rounded-lg border px-4" key={ticket.ticketId}>
            <Accordion selectionMode="multiple" className="w-full">
              <AccordionItem
                textValue={`${ticket.ticketId}: ${ticket.title}`}
                title={
                  <div className="flex justify-between">
                    <div className="flex gap-2">
                      <Checkbox
                        isSelected={tempSelectedItems.some(
                          (selected) => selected.ticketId === ticket.ticketId,
                        )}
                        onChange={() => handleSelectionChange(ticket)}
                      />
                      <p className="label-s whitespace-nowrap text-primary-teal-600">
                        {ticket.ticketId}:
                      </p>
                      <p className="label-s line-clamp-1 flex-grow break-words">{ticket.title}</p>
                    </div>
                    {ticket.url && (
                      <Link href={ticket.url} target="_blank">
                        <Image src="/icons/jira.svg" alt="jira logo" width={20} height={20} />
                      </Link>
                    )}
                  </div>
                }
              >
                <p className="line-clamp-3">
                  <Markdown content={ticket.description} />
                </p>
              </AccordionItem>
            </Accordion>
          </div>
        ))}
      </div>
    );
  };

  const triggerQaTestsGeneration = async () => {
    try {
      setShowGitlabValidation(true);

      const allGitlabLinks = getAllGitlabLinks(true);
      const hasInvalidGitlabLinks = allGitlabLinks.some(
        (link) => link.trim() !== '' && !validateGitlabUrl(link),
      );

      if (hasInvalidGitlabLinks) {
        return;
      }

      const firstNonEmptyEntry = Object.entries(selectedTickets).find(
        ([_, tickets]) => tickets.length > 0
      );

      if (!firstNonEmptyEntry) {
        return;
      }

      const [ticketTypeKey, tickets] = firstNonEmptyEntry;
      const ticketType = TICKET_TYPE_TO_SUBMODULE_MAP[ticketTypeKey as TicketCollectionKey];
      const ticketsList = tickets.map((item) => ({
        ticket_id: item.ticketId,
        ticket_title: item.title,
      }));
      setDisableSubmit(true);
      const gitlabLinks = getAllGitlabLinks();
      const response = await axiosInstance.post('/api/r2q/generate', {
        selected_tickets: ticketsList,
        platform: ConfigTypes.JIRA.toLocaleUpperCase(),
        type: ticketType,
        title: title,
        additional_input: additionalInput,
        commit_urls: gitlabLinks,
      });

      const chatId = response.data?.chat_id;
      router.push(`/${Modules.R2Q.toLocaleLowerCase()}/${chatId}`);
    } catch (error) {
      setDisableSubmit(false);

      // Handle specific error types from the backend
      if (error instanceof AxiosError && error.response) {
        const responseData = error.response.data;
        if (responseData?.error?.type === 'INVALID_COMMIT_URL') {
          showToast(ToastType.ERROR, r2qConstants('invalidCommitUrlError'));
          return;
        }
      }

      // Default error handling
      showToast(ToastType.ERROR, r2qConstants('errorMsg'));
      log.warn('Error in triggering QA Tests Generation', error);
    }
  };

  const modalConstants = useTranslations('R2Q.modal');
  const r2qConstants = useTranslations('R2Q');
  const formConstants = useTranslations('R2Q.formInput');
  const ticketSelectionConstants = useTranslations('Platform.ticketSelectionModal');

  const getTicketTypeLabels = (): Record<keyof TicketCollections, string> => ({
    epics: r2qConstants('selectedEpics'),
    stories: r2qConstants('selectedStories'),
    tasks: `${modalConstants('tasks')} selected`,
    bugs: `${modalConstants('bugs')} selected`,
    subBugs: `${modalConstants('subBugs')} selected`,
    subTasks: `${modalConstants('subTasks')} selected`,
  });

  const FooterContent = () => {
    return (
      <div className="flex justify-center gap-4">
        <Button
          variant={ButtonVariant.SOLID}
          className="text-white"
          onClick={confirmSelection}
          isDisabled={tempSelectedItems.length === 0}
        >
          {formConstants('selectButton')}
        </Button>
        <Button
          variant={ButtonVariant.BORDERED}
          className="text-primary-teal-600"
          onClick={closeModal}
        >
          {formConstants('cancelButton')}
        </Button>
      </div>
    );
  };

  const BodyContent = () => {
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
      if (isModalOpen && inputRef.current) {
        inputRef.current.focus();
      }
    }, [isModalOpen]);

    return (
      <div
        className="flex max-h-80 w-full flex-col gap-4 overflow-y-auto p-4"
        ref={modalContainerRef}
      >
        <div className="label-m flex w-full items-center text-center">
          {modalConstants('heading')}
        </div>
        {isLoading ? (
          <LoadingSpinner />
        ) : Object.values(allAIReady).every(tickets => tickets.length === 0) ? (
          <p className="label-s text-secondary-neutral-600">{modalConstants('noTicketsFound')}</p>
        ) : (
          <>
            <Dropdown
              triggerComponent={
                <div className="w-full rounded-lg border border-gray-300 px-4 py-2">
                  <p>
                    {selectedOption === 'epics' && modalConstants('epics')}
                    {selectedOption === 'user-stories' && modalConstants('stories')}
                    {selectedOption === 'tasks' && modalConstants('tasks')}
                    {selectedOption === 'bugs' && modalConstants('bugs')}
                    {selectedOption === 'sub-bugs' && modalConstants('subBugs')}
                    {selectedOption === 'sub-tasks' && modalConstants('subTasks')}
                  </p>
                </div>
              }
              options={[
                {
                  text: modalConstants('epics'),
                  onClick: () => {
                    setSelectedOption('epics');
                    setTempSelectedItems(selectedTickets.epics);
                  },
                },
                {
                  text: modalConstants('stories'),
                  onClick: () => {
                    setSelectedOption('user-stories');
                    setTempSelectedItems(selectedTickets.stories);
                  },
                },
                {
                  text: modalConstants('tasks'),
                  onClick: () => {
                    setSelectedOption('tasks');
                    setTempSelectedItems(selectedTickets.tasks);
                  },
                },
                {
                  text: modalConstants('bugs'),
                  onClick: () => {
                    setSelectedOption('bugs');
                    setTempSelectedItems(selectedTickets.bugs);
                  },
                },
                {
                  text: modalConstants('subBugs'),
                  onClick: () => {
                    setSelectedOption('sub-bugs');
                    setTempSelectedItems(selectedTickets.subBugs);
                  },
                },
                {
                  text: modalConstants('subTasks'),
                  onClick: () => {
                    setSelectedOption('sub-tasks');
                    setTempSelectedItems(selectedTickets.subTasks);
                  },
                },
              ]}
            />
            <Input
              type={InputType.TEXT}
              ref={inputRef}
              placeholder={ticketSelectionConstants('searchTickets')}
              value={searchQuery}
              endContent={<MagnifyingGlassIcon className="h-6 w-6" />}
              onChange={(e) => {
                setSearchQuery(e.target.value);
              }}
              className="mb-4"
            />
            {renderTicketsList()}
          </>
        )}
      </div>
    );
  };

  const areNoTicketsSelected = Object.values(selectedTickets).every(tickets => tickets.length === 0);

  const handleAdditionalInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAdditionalInput(e.target.value);
  };

  const handleGitlabLinkChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setGitlabLink(e.target.value);
    if (showGitlabValidation) {
      setShowGitlabValidation(false);
    }
  };

  const handleAdditionalGitlabLinkChange = (index: number, value: string) => {
    const updatedLinks = [...additionalGitlabLinks];
    updatedLinks[index] = value;
    setAdditionalGitlabLinks(updatedLinks);
    if (showGitlabValidation) {
      setShowGitlabValidation(false);
    }
  };

  const onAddGitlabLink = () => {
    setAdditionalGitlabLinks((prevLinks) => [...prevLinks, '']);
    setShowGitlabValidation(false);
  };

  const onRemoveGitlabLink = (index: number) => {
    setAdditionalGitlabLinks((prevLinks) => prevLinks.filter((_, i) => i !== index));
  };

  const validateGitlabUrl = (url: string): boolean => {
    if (!url.trim()) return true;

    const gitlabUrlPatterns = [
      /^https:\/\/gitlab\.abudhabi\.ae\/[\w-]+\/[\w-]+\/[\w-]+(?:\/[\w-]+)*\/-\/commit\/[a-f0-9]{40}$/,
      /^https:\/\/gitlab\.abudhabi\.ae\/[\w-]+\/[\w-]+\/[\w-]+(?:\/[\w-]+)*\/-\/merge_requests\/\d+\/diffs\?commit_id=[a-f0-9]{40}$/,
    ];

    return gitlabUrlPatterns.some((pattern) => pattern.test(url.trim()));
  };

  const getAllGitlabLinks = (includeEmpty: boolean = false): string[] => {
    const allLinks = [gitlabLink, ...additionalGitlabLinks];
    return includeEmpty ? allLinks : allLinks.filter((link) => link.trim() !== '');
  };

  const shouldShowGitlabError = (link: string): boolean => {
    return showGitlabValidation && link.trim() !== '' && !validateGitlabUrl(link);
  };

  return (
    <>
      <Input
        className="w-1/2"
        type={InputType.TEXT}
        placeholder={r2qConstants('titlePlaceholder')}
        onChange={handleTitleChange}
        isRequired={true}
        label={r2qConstants('title')}
      />

      <div className="flex flex-col gap-4">
        <div className="flex w-full items-center gap-6">
          {areNoTicketsSelected ? (
            <span className="flex items-center gap-1">
              <p className="label-s">{r2qConstants('noTicketsSelectedMessage')}</p>
              <p className="text-danger">{r2qConstants('requiredAsterisk')}</p>
            </span>
          ) : (
            <>
              {Object.entries(selectedTickets).map(([type, tickets]) =>
                tickets.length > 0 && (
                  <p key={type} className="label-s">
                    {tickets.length} {getTicketTypeLabels()[type as keyof TicketCollections]}
                  </p>
                )
              )}
            </>
          )}
          <Button
            variant={ButtonVariant.FLAT}
            className="h-8 w-fit items-center rounded-xl border bg-white px-3"
            onClick={openModal}
          >
            {modalConstants('selectTickets')}
          </Button>
        </div>
        {Object.entries(selectedTickets).map(([type, tickets]) =>
          tickets.length > 0 && (
            <SelectedTickets
              key={type}
              tickets={tickets}
              handleRemoveTicket={removeSelectedTicket}
            />
          )
        )}
      </div>

      <div className="flex flex-col gap-4">
        <div
          className={`flex gap-6 ${shouldShowGitlabError(gitlabLink) ? 'items-center' : 'items-end'}`}
        >
          <Input
            type={InputType.TEXT}
            label={r2qConstants('gitlabLink')}
            placeholder={r2qConstants('gitlabLinkPlaceholder')}
            value={gitlabLink}
            onChange={handleGitlabLinkChange}
            isInvalid={shouldShowGitlabError(gitlabLink)}
            errorMessage={
              shouldShowGitlabError(gitlabLink) ? r2qConstants('gitlabLinkInvalid') : ''
            }
            className="w-1/2"
          />
          <Button
            variant={ButtonVariant.FLAT}
            className="h-8 w-fit items-center rounded-xl border bg-white px-3"
            onClick={onAddGitlabLink}
          >
            {r2qConstants('addMoreGitlabLinksButton')}
          </Button>
        </div>

        {additionalGitlabLinks.map((link, index) => (
          <div className="flex items-center gap-4" key={index}>
            <Input
              type={InputType.TEXT}
              label={`${r2qConstants('gitlabLink')} ${index + 2}`}
              placeholder={r2qConstants('gitlabLinkPlaceholder')}
              value={link}
              onChange={(e) => handleAdditionalGitlabLinkChange(index, e.target.value)}
              isInvalid={shouldShowGitlabError(link)}
              errorMessage={shouldShowGitlabError(link) ? r2qConstants('gitlabLinkInvalid') : ''}
              className="w-1/2"
            />
            <MinusCircleIcon
              className="mt-4 h-5 w-5 cursor-pointer text-secondary-neutral-600"
              onClick={() => onRemoveGitlabLink(index)}
            />
          </div>
        ))}
      </div>

      <Textarea
        placeholder={r2qConstants('additionalInputPlaceholder')}
        onChange={handleAdditionalInputChange}
        label={r2qConstants('additionalInput')}
      />

      <Divider className="my-6" />

      <Button
        className="w-fit"
        onClick={triggerQaTestsGeneration}
        isDisabled={
          areNoTicketsSelected ||
          title.length === 0 ||
          disableSubmit
        }
      >
        {r2qConstants('generateQATestsButton')}
      </Button>
      <Modal
        size={ModalSize['3XL']}
        bodyContent={<BodyContent />}
        footerContent={<FooterContent />}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </>
  );
};

export default QaTestsGenerationForm;
