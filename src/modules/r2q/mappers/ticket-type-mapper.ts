import { TicketCollections } from '@/modules/platform/interfaces/tickets';
import { SubModules } from '@/modules/platform/interfaces/modules';

/**
 * Type representing the option keys used in the UI dropdown
 */
export type TicketOptionKey =
  | 'epics'
  | 'user-stories'
  | 'tasks'
  | 'bugs'
  | 'sub-bugs'
  | 'sub-tasks';

/**
 * Type representing the keys of TicketCollections
 */
export type TicketCollectionKey = keyof TicketCollections;

/**
 * Maps UI option keys to TicketCollections property keys
 * This mapper is used to convert dropdown selection values to the corresponding
 * property names in the TicketCollections type
 */
export const OPTION_TO_TICKET_TYPE_MAP: Record<TicketOptionKey, TicketCollectionKey> = {
  epics: 'epics',
  'user-stories': 'stories',
  tasks: 'tasks',
  bugs: 'bugs',
  'sub-bugs': 'subBugs',
  'sub-tasks': 'subTasks',
} as const;

/**
 * Maps TicketCollections property keys to SubModules enum values
 * This mapper is used to convert TicketCollections keys to the corresponding
 * SubModules enum values for API requests
 */
export const TICKET_TYPE_TO_SUBMODULE_MAP: Record<TicketCollectionKey, SubModules> = {
  epics: SubModules.EPICS,
  stories: SubModules.USER_STORIES,
  tasks: SubModules.TASKS,
  bugs: SubModules.BUGS,
  subBugs: SubModules.SUB_BUGS,
  subTasks: SubModules.SUB_TASKS,
} as const;

/**
 * Helper function to get TicketCollections key from option key
 * @param optionKey - The option key from UI dropdown
 * @returns The corresponding TicketCollections key or undefined if not found
 */
export const getTicketTypeFromOption = (optionKey: string): TicketCollectionKey | undefined => {
  return OPTION_TO_TICKET_TYPE_MAP[optionKey as TicketOptionKey];
};

/**
 * Helper function to get SubModule from TicketCollections key
 * @param ticketType - The TicketCollections key
 * @returns The corresponding SubModules enum value
 */
export const getSubModuleFromTicketType = (ticketType: TicketCollectionKey): SubModules => {
  return TICKET_TYPE_TO_SUBMODULE_MAP[ticketType];
};

/**
 * Helper function to get SubModule directly from option key
 * @param optionKey - The option key from UI dropdown
 * @returns The corresponding SubModules enum value or undefined if not found
 */
export const getSubModuleFromOption = (optionKey: string): SubModules | undefined => {
  const ticketType = getTicketTypeFromOption(optionKey);
  return ticketType ? getSubModuleFromTicketType(ticketType) : undefined;
};
