import { useCallback, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import axiosInstance from '@/utils/axios';

import { useChatContext } from '../contexts/chat.context';

import { multiplyRefetchInterval } from '@/utils/multiply-refetch-interval';
import { areJobsComplete, hasJobFailed } from '@/modules/platform/utils/job-status';

import { Job } from '@/modules/platform/interfaces/job';
import { ChatMessage } from '@/modules/i2r/components/chat-box';

const LOADING_MESSAGE_TEXT = 'loading-';
const USER_MESSAGE_TEXT = 'user-';
const FILE_UPLOAD_MESSAGE_TEXT = 'file-upload-';

interface UseChatMessagesProps {
  chatId: string;
  jiraConfig?: any;
}

interface FormattedMessage {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: string;
  showActions: boolean;
}

export function useChatMessages({ chatId, jiraConfig }: UseChatMessagesProps) {
  const {
    messages,
    setMessages,
    isLoading,
    setIsLoading,
    chatPollingEnabled,
    setChatPollingEnabled,
  } = useChatContext();

  // State for file upload handling
  const [isFileUploading, setIsFileUploading] = useState(false);
  const [fileUploadJobId, setFileUploadJobId] = useState<string | null>(null);
  const [uploadedChatId, setUploadedChatId] = useState<string | null>(null);

  const formatChatMessage = useCallback((msg: any): FormattedMessage => {
    const messageType = msg.messageType || msg.message_type || 'AI';
    const displayMessage = msg.displayMessage || msg.display_message || msg.text || '';
    const sender = messageType === 'HUMAN' ? 'user' : 'ai';
    const subType = msg.sub_type || msg.subType || '';
    const shouldShowActions = messageType === 'AI' && subType !== 'CHAT';
    
    return {
      id: msg.id,
      sender,
      text: displayMessage,
      timestamp: msg.created_at || new Date().toISOString(),
      showActions: shouldShowActions,
    };
  }, []);

  const createUserMessage = useCallback((text: string): ChatMessage => ({
    id: `${USER_MESSAGE_TEXT}${Date.now()}-${Math.random()}`,
    sender: 'user',
    text,
  }), []);

  const createLoadingMessage = useCallback((): ChatMessage => ({
    id: `${LOADING_MESSAGE_TEXT}${Date.now()}-${Math.random()}`,
    sender: 'ai',
    text: '', // Empty text for skeleton loader
    showActions: false,
    isLoading: true,
  }), []);

  // API calls
  const fetchChatHistory = useCallback(async (chatId: string) => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.get('/api/platform/chat-message', {
        params: { chatId },
      });
      
      const chatHistory = response.data || [];
      const formattedMessages = chatHistory.map(formatChatMessage);
      setMessages(formattedMessages);
    } catch (error) {
      console.error('Error fetching chat history:', error);
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  }, [setMessages, setIsLoading, formatChatMessage]);

  const invokeChatAssistant = useCallback(async (message: string) => {
    if (!jiraConfig) {
      console.error('Jira config is required');
      return;
    }
    
    if (!jiraConfig.id) {
      console.error('Jira config ID is required');
      return;
    }

    const effectiveChatId = uploadedChatId || chatId;

    const payload: any = {
      prompt: message,
      module: 'I2R',
      config_id: jiraConfig?.id,
      chat_id: effectiveChatId,
      i2r_metadata: {
        idea: 'string',
        idea_from_scratch: true,
      },
    };

    console.log('Invoke payload:', payload);
    await axiosInstance.post('/api/i2r/chat-assistant-invoke', payload);
  }, [jiraConfig, chatId, uploadedChatId]);

  // File upload handler for existing chats
  const handleFileUpload = useCallback(async (fileData: any) => {
    if (!jiraConfig) {
      console.error('Jira config is required for file upload');
      return;
    }

    setIsFileUploading(true);
    
    try {
      // Create FormData exactly as the backend expects (data: {} and file: {})
      const formData = new FormData();
      
      // Add the data field as JSON string (exactly as Swagger shows)
      const dataObj = {
        module: 'I2R',
        chat_id: chatId,
      };
      formData.append('data', JSON.stringify(dataObj));          
      formData.append('file', fileData);
      for (const [key, value] of formData.entries()) {
        console.log(`FormData entry - ${key}:`, value);
      }

      const uploadResponse = await axiosInstance.post('/api/i2r/chat-file-upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      if (uploadResponse.data.job_id) {
        setFileUploadJobId(uploadResponse.data.job_id);
        console.log('File upload job started:', uploadResponse.data.job_id);
      }
      
      const { chat_id } = uploadResponse.data;
      if (chat_id) {
        setUploadedChatId(chat_id);
        console.log('File upload completed, new chat ID:', chat_id);
      }

    } catch (error) {
      console.error('Error uploading file:', error);
      // Remove file upload message on error
      setMessages((prev) => prev.filter(msg => !msg.id.startsWith(FILE_UPLOAD_MESSAGE_TEXT)));
    } finally {
      setIsFileUploading(false);
    }
  }, [jiraConfig, chatId, setMessages, invokeChatAssistant, setChatPollingEnabled]);

  // File delete handler
  const handleFileDelete = useCallback(async (fileName: string) => {
    try {
      await axiosInstance.delete('/api/i2r/chat-file-delete', {
        params: {
          chatId: chatId,
          fileName: fileName,
        },
      });

      console.log('File deleted successfully:', fileName);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error; // Re-throw to allow component to handle the error
    }
  }, [chatId]);

  // Job polling for both chat and file upload
  const fetchJobs = useCallback(async (): Promise<Job[]> => {
    try {
      const effectiveChatId = uploadedChatId || chatId;
      const response = await axiosInstance.get('/api/platform/jobs', {
        params: { chatId: effectiveChatId },
      });
      
      const allJobs = response.data;
      const chatJobs = allJobs.filter((job: Job) => job.sub_type === 'CHAT');
      const fileUploadJobs = allJobs.filter((job: Job) => job.sub_type === 'FILE_UPLOAD');
      
      // Handle chat jobs
      if (areJobsComplete(chatJobs) && chatPollingEnabled) {
        setChatPollingEnabled(false);
        await fetchChatHistory(effectiveChatId);
      }
      
      const failedChatJobs = chatJobs.filter((job: Job) => hasJobFailed(job));
      if (failedChatJobs.length > 0 && chatPollingEnabled) {
        setChatPollingEnabled(false);
        setMessages((prev) => 
          prev.map(msg =>
            msg.id.startsWith(LOADING_MESSAGE_TEXT)
              ? { ...msg, text: 'Sorry, the request failed. Please try again.', showActions: false, isLoading: false }
              : msg
          )
        );
      }

      // Handle file upload jobs
      if (fileUploadJobId) {
        const fileJob = fileUploadJobs.find((job: Job) => job.id === fileUploadJobId);
        if (fileJob) {
          if (hasJobFailed(fileJob)) {
            console.error('File upload failed:', fileJob);
            setFileUploadJobId(null);
            setMessages((prev) => 
              prev.map(msg =>
                msg.id.startsWith(FILE_UPLOAD_MESSAGE_TEXT)
                  ? { ...msg, text: `❌ Failed to upload: ${msg.text.split(': ')[1]}`, sender: 'user' as const, isLoading: false }
                  : msg
              )
            );
          } else if (areJobsComplete([fileJob])) {
            console.log('File upload completed successfully');
            setFileUploadJobId(null);
            // Update file upload message to show completion
            setMessages((prev) => 
              prev.map(msg =>
                msg.id.startsWith(FILE_UPLOAD_MESSAGE_TEXT)
                  ? { ...msg, text: `✅ Uploaded: ${msg.text.split(': ')[1]}`, isLoading: false }
                  : msg
              )
            );
          } else {
            // Job is still in progress, keep loading state
            console.log('File upload job in progress:', fileJob.status);
          }
        }
      }
      
      return response.data;
    } catch (error) {
      console.error('Error fetching jobs:', error);
      setChatPollingEnabled(false);
      return [];
    }
      }, [chatId, uploadedChatId, chatPollingEnabled, setChatPollingEnabled, fetchChatHistory, setMessages, fileUploadJobId]);

  // Query for job polling
  useQuery<Job[], Error>({
    queryKey: ['fetchJobs', chatId, uploadedChatId, fileUploadJobId],
    queryFn: () => fetchJobs(),
    enabled: !!(chatId || uploadedChatId) && (chatPollingEnabled || !!fileUploadJobId),
    refetchInterval: multiplyRefetchInterval,
  } as any);

  // Message sending handler
  const handleSend = useCallback(async (message: string) => {
    if (!jiraConfig) return;
    
    const userMessage = createUserMessage(message);
    setMessages((prev) => [...prev, userMessage]);
    
    const loadingMessage = createLoadingMessage();
    setMessages((prev) => [...prev, loadingMessage]);
    
    try {
      await invokeChatAssistant(message);
      setChatPollingEnabled(true);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prev) => prev.filter(msg => !msg.id.startsWith(LOADING_MESSAGE_TEXT)));
    }
  }, [jiraConfig, setMessages, createUserMessage, createLoadingMessage, invokeChatAssistant, setChatPollingEnabled]);

  useEffect(() => {
    if (!chatPollingEnabled) {
      setMessages((prev) => 
        prev.map(msg =>
          msg.id.startsWith(LOADING_MESSAGE_TEXT)
            ? { ...msg, isLoading: false }
            : msg
        )
      );
    }
  }, [chatPollingEnabled, setMessages]);

  useEffect(() => {
    if (chatId) {
      fetchChatHistory(chatId);
    }
  }, [chatId, fetchChatHistory]);

  return {
    messages,
    isLoading,
    handleSend,
    handleFileUpload,
    handleFileDelete,
    isFileUploading,
    fileUploadJobId
  };
} 