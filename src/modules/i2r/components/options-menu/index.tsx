import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import { ConfigContext } from '@/modules/platform/contexts/config.context';
import {
  ArrowPathIcon,
  HandThumbDownIcon,
  HandThumbUpIcon,
  PencilIcon,
  ArrowTopRightOnSquareIcon,
} from '@heroicons/react/24/outline';
import Image from 'next/image';
import React, { useContext, useEffect, useState } from 'react';
import { Story, TicketData } from '../../interfaces';
import { useI2RPollingContext } from '../../contexts/polling.context';
import Dropdown from '@/components/dropdown';
import { useTranslations } from 'next-intl';
import log from '@/utils/logger';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { Job } from '@/modules/platform/interfaces/job';
import { useQuery } from '@tanstack/react-query';
import showToast, { ToastType } from '@/utils/toast';
import axiosInstance from '@/utils/axios';

type SelectedUserStoriesType = { [key: string]: Story[] };

interface IOptionsMenuProps {
  isLikeEnabled?: boolean;
  isDislikeEnabled?: boolean;
  isRegenerateEnabled?: boolean;
  isEditEnabled?: boolean;
  showPublish?: boolean;
  showLeftSideOptions?: boolean;
  isPublished?: boolean;
  openRegenerationModal: () => void;
  setRegenerationConfig: React.Dispatch<React.SetStateAction<{ id: string; type: string }>>;
  onEdit?: () => void;
  type: string;
  id: string;
  setPollingEnabled?: React.Dispatch<React.SetStateAction<boolean>>;
  ticketData?: TicketData[];
  url?: string;
  areStoriesGenerated?: boolean;
  selectedUserStories?: SelectedUserStoriesType;
  epicId?: string;
  selectedTab?: string;
  isEpic?: boolean;
  generatedStories?: Story[];
  showOpen?: boolean;
  onOpen?: () => void;
  setPrdData?: (fn: (prev: any) => any) => void;
  showEditButton?: boolean;
}

interface IOptionProps {
  icon?: React.ReactNode;
  text: string;
  isDisabled: boolean;
  isLoading?: boolean;
  onClick?: () => void;
}

export const Option = ({ icon, text, isDisabled, isLoading = false, onClick }: IOptionProps) => {
  return (
    <Button
      className="flex h-8 w-fit items-center gap-2 rounded-xl border bg-white px-3"
      variant={ButtonVariant.FLAT}
      isDisabled={isDisabled}
      startIcon={icon}
      onClick={onClick}
      isLoading={isLoading}
    >
      <div className="label-xs text-secondary-neutral-600">{text}</div>
    </Button>
  );
};

const OptionsMenu = ({
  isLikeEnabled = true,
  isDislikeEnabled = true,
  isRegenerateEnabled = true,
  isEditEnabled = true,
  showPublish = true,
  showLeftSideOptions = true,
  areStoriesGenerated = false,
  isPublished: isPublishedInit,
  openRegenerationModal,
  setRegenerationConfig,
  onEdit,
  type,
  id,
  setPollingEnabled,
  url = '',
  selectedUserStories,
  epicId,
  selectedTab,
  isEpic,
  generatedStories,
  showOpen = false,
  onOpen,
  setPrdData,
  showEditButton = true,
}: IOptionsMenuProps) => {
  const [likeDisabled, setLikeDisabled] = useState(!isLikeEnabled);
  const [dislikeDisabled, setDislikeDisabled] = useState(!isDislikeEnabled);
  const [isPublishButtonLoading, setIsPublishButtonLoading] = useState<boolean>(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [failureMessage, setFailureMessage] = useState<string | null>(null);
  const [storedPublishChatId, setStoredPublishChatId] = useState<string | null>(null);
  const [queryDisabled, setQueryDisabled] = useState(false);

  const {
    pollingAfterPublish,
    setPollingAfterPublish,
    publishingTicketIds,
    setPublishingTicketIds,
    epicsWithStories,
  } = useI2RPollingContext();

  const optionsMenuConstants = useTranslations('Platform.optionsMenu');
  const publishConstants = useTranslations('I2R.publish');
  const tabsConstants = useTranslations('I2R.homepage.tabs');

  const unPublishedStories =
    epicId && selectedUserStories && selectedUserStories[epicId]
      ? selectedUserStories[epicId].filter(
          (story) => !story.url && !story.is_published && !publishingTicketIds.includes(story.id),
        )
      : [];

  const openModalForRegeneration = () => {
    const config = {
      id: id,
      type: type,
    };
    setRegenerationConfig(config);
    openRegenerationModal();
  };
  const [isOpen, setIsOpen] = useState(false);
  const [isPublished, setIsPublished] = useState(isPublishedInit);
  const { jiraConfig } = useContext(ConfigContext);
  const projectIds = jiraConfig?.boardIds || [];

  const handlePublish = async (boardId: string) => {
    setQueryDisabled(false);

    try {
      // Clear any previous validation errors when attempting to publish
      setValidationErrors([]);
      setIsOpen(false);
      let payload;
      if (unPublishedStories?.length === 0 && selectedTab === tabsConstants('stories')) {
        showToast(ToastType.ERROR, publishConstants('noStoriesSelectedError'));
        return;
      }
      if (selectedTab === tabsConstants('epics')) {
        payload = {
          ticket_ids: [id],
          board_id: boardId,
        };
      } else {
        payload = {
          board_id: boardId,
          ticket_ids: unPublishedStories?.map((story) => story.id) || [],
        };
      }

      setIsPublishButtonLoading(true); // Start loading immediately

      const response = await axiosInstance.post('/api/platform/publish', payload);
      if (response.data && response.data.chat_id) {
        // Log both IDs to see which one is needed for job fetching
        const publishChatId = response.data.chat_id;

        // Store this chat ID for future use
        setStoredPublishChatId(publishChatId);

        if (selectedTab === tabsConstants('epics')) {
          setPublishingTicketIds((prev: string[]) => [...prev, id]);
        } else {
          setPublishingTicketIds((prev: string[]) => [
            ...prev,
            ...(unPublishedStories?.map((story) => story.id) || []),
          ]);
        }
        setPollingAfterPublish(true);

        // Immediately check for validation errors
      } else {
        // Handle case where response doesn't have expected data
        setIsPublishButtonLoading(false);
        // setPublishCancelled(true);
        showToast(
          ToastType.ERROR,
          publishConstants('publishFailed') || 'Failed to publish to Jira',
        );
      }
    } catch (error) {
      log.warn('Error occurred while publishing to Jira', error);
      // Reset loading state and show error
      setIsPublishButtonLoading(false);
      setPollingAfterPublish(true);
      // setPublishCancelled(true);
      // Remove any ticket IDs that were being published
      if (selectedTab === tabsConstants('epics')) {
        setPublishingTicketIds((prev: string[]) => prev.filter((ticketId) => ticketId !== id));
      } else {
        const storyIds = unPublishedStories?.map((story) => story.id) || [];
        setPublishingTicketIds((prev: string[]) =>
          prev.filter((ticketId) => !storyIds.includes(ticketId)),
        );
      }

      // Reset polling state
      setPollingAfterPublish(false);

      // Show error toast
      showToast(ToastType.ERROR, publishConstants('publishFailed') || 'Failed to publish to Jira');
    }
  };

  // Add this query to fetch job status
  // Add this query to fetch job status
  const { data: jobs } = useQuery<Job[]>({
    queryKey: ['fetchJobs', id, storedPublishChatId],
    queryFn: async () => {
      // Use the stored publish chat ID if available, otherwise use the original id
      const chatIdToUse = storedPublishChatId || id;

      if (!pollingAfterPublish) return [];
      try {
        const response = await axiosInstance.get('/api/platform/jobs', {
          params: { chatId: chatIdToUse },
        });
        return response.data;
      } catch (error) {
        log.warn('Error fetching job status', error);
        return [];
      }
    },
    enabled:
      !queryDisabled &&
      pollingAfterPublish &&
      (publishingTicketIds.includes(id) || storedPublishChatId !== null),
    refetchInterval: pollingAfterPublish ? 2000 : false,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  const handlePublishedClick = () => {
    if (isPublished && url) {
      window.open(url, '_blank');
    }
  };

  const handleLikeClick = async (liked: boolean) => {
    try {
      const response = await axiosInstance.post('/api/platform/like-dislike', {
        id: id,
        liked: liked,
        type: type,
      });
      if (response.data) {
        if (response.data.liked) {
          setLikeDisabled(true);
          setDislikeDisabled(false);
          if (setPrdData) setPrdData((prev: any) => ({ ...prev, liked: true })); // update context
        } else {
          setLikeDisabled(false);
          setDislikeDisabled(true);
          if (setPrdData) setPrdData((prev: any) => ({ ...prev, liked: false })); // update context
        }
        if (setPollingEnabled) {
          setPollingEnabled(true);
        }
      } else {
        log.warn('Failed to update the ticket');
      }
    } catch (error) {
      log.warn('Error updating the ticket:', error);
    }
  };

  const getPublishTitle = (storiesCount: number) => {
    if (selectedTab === tabsConstants('epics') || storiesCount === 0) {
      return publishConstants('noStories');
    } else if (storiesCount === 1) {
      return publishConstants('singleStory');
    }
    return publishConstants('multipleStories', { storiesCount });
  };

  const isPublishButtonDisabled = isPublished
    ? false // view published link
    : !areStoriesGenerated ||
      (pollingAfterPublish && publishingTicketIds.includes(id)) ||
      Boolean(
        epicsWithStories?.find((epic) => epic.id === epicId && epic.is_published && !epic.url),
      );

  useEffect(() => {
    // Remove the ticket id from the list of tickets being published
    if (url) {
      setPublishingTicketIds((prev) => {
        if (selectedTab === tabsConstants('epics')) {
          return prev.filter((tid) => tid !== id);
        } else {
          const allStoryIds =
            epicId && selectedUserStories?.[epicId]
              ? selectedUserStories[epicId].map((s) => s.id)
              : [];
          return prev.filter((tid) => !allStoryIds.includes(tid));
        }
      });
      setValidationErrors([]);
    }

    // Removing the published user stories ticket ids from the list of tickets being published when the generated user stories accordion is closed
    if (type === SubModules.EPICS) {
      const currentEpic = epicsWithStories?.find((epic) => epic.id === epicId);
      currentEpic?.stories?.forEach((story) => {
        if (publishingTicketIds.includes(story.id) && story.url) {
          setPublishingTicketIds((prev: string[]) =>
            prev.filter((ticketId) => ticketId !== story.id),
          );
        }
      });
    }

    if (selectedTab === tabsConstants('stories') && isEpic && generatedStories) {
      const areAllStoriesPublished = generatedStories.every((story) => story.url);
      setIsPublished(areAllStoriesPublished && Boolean(url));
    } else {
      setIsPublished(Boolean(url));
    }
  }, [epicsWithStories, url]);

  useEffect(() => {
    if (!jobs || jobs.length === 0) {
      log.info('No jobs received.');
      return;
    }

    // Find the latest job with a JiraTicketValidationError
    const matchingJob = jobs.find((job) => {
      const isMatch =
        job.status === 'FAILED' &&
        Array.isArray(job.metadata?.validation_messages) &&
        job.metadata.validation_messages.length > 0;

      return isMatch;
    });

    if (!matchingJob) {
      log.info('No validation error jobs found');
      return;
    }

    const messages = Array.isArray(matchingJob.metadata.validation_messages)
      ? matchingJob.metadata.validation_messages
      : [];
    const failureTypeMessage = matchingJob.metadata.failure_type;

    setValidationErrors(messages);
    setFailureMessage(failureTypeMessage);
    setPollingAfterPublish(false);
    setIsPublishButtonLoading(false);
    setPublishingTicketIds((prev) => prev.filter((tid) => tid !== id));
    setStoredPublishChatId(null);
    setQueryDisabled(true);
  }, [jobs]);

  useEffect(() => {
    let shouldBeLoading = false;

    if (selectedTab === tabsConstants('epics')) {
      shouldBeLoading = publishingTicketIds.includes(id);
    } else {
      const storyIds =
        epicId && selectedUserStories?.[epicId] ? selectedUserStories[epicId].map((s) => s.id) : [];
      shouldBeLoading = storyIds.some((sid) => publishingTicketIds.includes(sid));
    }

    setIsPublishButtonLoading(pollingAfterPublish && shouldBeLoading);
  }, [pollingAfterPublish, publishingTicketIds, selectedTab, id, epicId, selectedUserStories]);

  useEffect(() => {
    setLikeDisabled(!isLikeEnabled);
    setDislikeDisabled(!isDislikeEnabled);
  }, [isLikeEnabled, isDislikeEnabled]);

  useEffect(() => {
    const published = isPublished || Boolean(url);
    if (published && validationErrors?.length > 0) {
      setValidationErrors([]);
    }
  }, []); // ← only runs once on first render
  // Find the return statement at the end of the component (around line 450-500)
  return (
    <div className="flex flex-grow justify-between">
      {showLeftSideOptions && (
        <div className="flex gap-4">
          <Option
            text={optionsMenuConstants('like')}
            icon={<HandThumbUpIcon width={20} height={20} />}
            isDisabled={likeDisabled}
            onClick={() => handleLikeClick(true)}
          />
          <Option
            text={optionsMenuConstants('dislike')}
            icon={<HandThumbDownIcon width={20} height={20} />}
            isDisabled={dislikeDisabled}
            onClick={() => handleLikeClick(false)}
          />
          <Option
            text={optionsMenuConstants('regenerate')}
            icon={<ArrowPathIcon width={20} height={20} />}
            isDisabled={!isRegenerateEnabled}
            onClick={openModalForRegeneration}
          />
          {showEditButton && (
            <Option
              text={optionsMenuConstants('edit')}
              icon={<PencilIcon width={20} height={20} />}
              isDisabled={!isEditEnabled}
              onClick={onEdit}
            />
          )}
          {showOpen && (
            <Option
              text={"Open"}
              icon={<ArrowTopRightOnSquareIcon width={20} height={20} />}
              isDisabled={false}
              onClick={onOpen}
            />
          )}
        </div>
      )}
      {showPublish && (
        <div className="relative" data-testid="publish-button">
          <Dropdown
            triggerComponent={
              <div className="ml-auto">
                <Option
                  text={
                    isPublished
                      ? publishConstants('jiraLink')
                      : getPublishTitle(unPublishedStories?.length || 0)
                  }
                  icon={<Image src="/icons/jira.svg" alt="jira logo" width={20} height={20} />}
                  isDisabled={isPublishButtonDisabled}
                  isLoading={isPublishButtonLoading}
                  onClick={isPublished ? handlePublishedClick : () => setIsOpen(!isOpen)}
                />
              </div>
            }
            isOpen={!isPublishButtonDisabled && !isPublishButtonLoading && isOpen}
            onOpenChange={(open) => setIsOpen(open)}
            options={projectIds.map((projectId) => ({
              text: projectId.toString(),
              onClick: () => {
                handlePublish(projectId.toString());
              },
            }))}
          />

          {validationErrors.length > 0 && (
            <div className="absolute right-0 z-40 mt-2 w-80 rounded-lg border border-red-300 bg-red-100 px-4 py-3 text-sm text-red-800 shadow-lg">
              <div className="flex items-center justify-between">
                <span className="font-semibold">⚠️ {failureMessage}</span>
                <button
                  className="text-xs text-red-600 hover:underline"
                  onClick={() => setValidationErrors([])}
                >
                  Dismiss
                </button>
              </div>
              <ul className="mt-2 list-disc space-y-1 pl-5">
                {validationErrors.map((error, index) => {
                  const [field, ...rest] = error.split(':');
                  const message = rest.join(':').trim();
                  return (
                    <li key={index}>
                      <strong>{field.trim()}:</strong> {message}
                    </li>
                  );
                })}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OptionsMenu;
