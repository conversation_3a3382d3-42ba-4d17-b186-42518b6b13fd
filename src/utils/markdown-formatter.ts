import { IDiagram } from '@/modules/r2diag/interfaces/diagram';

export const formatMarkdownWithMermaid = (content: string, diagrams: Array<IDiagram>) => {
  if (!diagrams) return content;

  return content?.replace(/<!(\w+)!>/g, (match, diagramType) => {
    const mapping = diagrams.find((item) => item.type === diagramType);
    return mapping ? '\n```mermaid\n' + mapping.code + '\n```\n' : match;
  });
};

export const formatTextWithLineBreaks = (text: string): string => {
  if (!text) return '';
  return text.replace(/\n/g, '  \n');
};
